#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 全面特征分析工具
合并P.py中所有特征集，检测噪声特征、重复特征、数据泄漏问题
"""

import logging
import pandas as pd
import numpy as np
from typing import List, Dict, Set, Tuple
import warnings
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ComprehensiveFeatureAnalyzer:
    """全面特征分析器"""
    
    def __init__(self):
        self.all_feature_sets = self._collect_all_feature_sets()
        self.merged_features = self._merge_all_features()
        self.analysis_results = {}
        
    def _collect_all_feature_sets(self) -> Dict[str, List[str]]:
        """收集P.py中所有的特征集定义"""
        feature_sets = {
            # 1. 基础特征集 (21个)
            'EFFECTIVE_FEATURES': [
                'pct_chg', 'change', 'high', 'low', 'close', 'open', 'pre_close', 'vol',
                'amount', 'turnover_rate', 'turnover_rate_f', 'volume_ratio', 'pe',
                'pe_ttm', 'pb', 'ps', 'ps_ttm', 'dv_ratio', 'dv_ttm', 'total_share', 'float_share'
            ],
            
            # 2. 基础价格特征
            'BASIC_PRICE_FEATURES': [
                'open', 'high', 'low', 'close', 'pre_close', 'pct_chg', 'change'
            ],
            
            # 3. 成交量特征
            'VOLUME_FEATURES': [
                'vol', 'amount', 'turnover_rate', 'turnover_rate_f', 'volume_ratio',
                'volume_ma3', 'volume_ma5', 'volume_ma10', 'volume_trend',
                'volume_explosion', 'volume_shrink', 'unusual_volume'
            ],
            
            # 4. 技术指标特征
            'TECHNICAL_FEATURES': [
                'ma3', 'ma5', 'ma10', 'ma20', 'ma60',
                'rsi2', 'rsi6', 'rsi14', 'rsi', 'macd', 'macd_hist', 'macd_cross_up',
                'kdj_k', 'kdj_d', 'cci', 'adx', 'dmi_plus', 'dmi_minus',
                'atr', 'boll_upper', 'boll_lower', 'boll_bandwidth',
                'wr_14', 'mfi', 'obv'
            ],
            
            # 5. 涨停相关特征
            'LIMIT_UP_FEATURES': [
                '连续涨停天数', '近期涨停次数', '最近涨停距离', '涨停强度',
                '涨停回封强度', '开盘涨停', '涨停合成概率', '涨停开板天数比',
                '首板后天板率', '连板成功率', '连板接力概率'
            ],
            
            # 6. 资金流特征
            'MONEY_FLOW_FEATURES': [
                'net_mf_amount', '主力净流入占比', '资金流入强度',
                '大单净流入', '中单净流入', '小单净流入',
                '超大单净流入', '机构净流入'
            ],
            
            # 7. 市场环境特征
            'MARKET_FEATURES': [
                'market_code', 'market_avg_pct', 'market_pct_std', 'market_avg_volume_ratio',
                'daily_limit_up_count', 'market_sentiment', 'market_volatility'
            ],
            
            # 8. 板块特征
            'SECTOR_FEATURES': [
                '板块联动强度', '板块涨停数量', '板块平均涨幅', '板块资金流入',
                'sector_momentum', 'sector_strength', 'sector_hot_rank'
            ],
            
            # 9. 热度特征
            'HOT_FEATURES': [
                'max_hot', 'avg_hot', 'best_rank', 'hot_trend', 'rank_improvement',
                'is_on_hot_list', 'is_on_ths_hot', '短线交易热度'
            ],
            
            # 10. 筹码特征
            'CHIP_FEATURES': [
                'weight_avg', 'winner_rate', 'cost_5pct', 'cost_15pct', 'cost_50pct',
                'cost_85pct', 'cost_95pct', 'weight_avg_change', 'winner_rate_change',
                'cost_spread', 'cost_concentration', 'cost_pressure', '筹码松散度'
            ],
            
            # 11. 波动性特征
            'VOLATILITY_FEATURES': [
                'daily_volatility', 'volatility_ma5', 'GK波动率', '日内高低比',
                '日内高低比变化', 'volatility_acc', 'volatility_acc_5',
                '振幅', '振幅_ma5', '振幅_ma10', '振幅_rank', '真实振幅'
            ],
            
            # 12. 革命性特征 (特征工程创建)
            'REVOLUTIONARY_FEATURES': [
                '革命性涨停预测得分', '革命性涨停概率', '量价配合度',
                '技术指标合成信号', '资金技术共振', '涨停资金共振',
                '热点技术共振', '首板市场适应度', '连板持续概率'
            ],
            
            # 13. 时间特征
            'TIME_FEATURES': [
                'trade_date', 'year', 'month', 'day', 'weekday',
                'is_month_end', 'is_quarter_end', 'is_year_end'
            ],
            
            # 14. 估值特征
            'VALUATION_FEATURES': [
                'pe', 'pe_ttm', 'pb', 'ps', 'ps_ttm', 'dv_ratio', 'dv_ttm',
                'total_share', 'float_share', 'total_mv', 'circ_mv'
            ],
            
            # 15. 超短线特征
            'ULTRA_SHORT_FEATURES': [
                '最近一次涨停偏离度', '超短反转信号', '竞价异动', '承接力度',
                '上板开板次数', '北交所强度指标', '科创板活跃度', '创业板短线强度',
                '跨市场资金流动', '日内波动幅度', '抢筹强度', '分时走势强度',
                '资金博弈强度', '冲高回落指标', '波段交易信号'
            ]
        }
        
        logging.info(f"✅ 收集到{len(feature_sets)}个特征集")
        for name, features in feature_sets.items():
            logging.info(f"  - {name}: {len(features)}个特征")
            
        return feature_sets
    
    def _merge_all_features(self) -> List[str]:
        """合并所有特征集，去重"""
        all_features = set()
        
        for feature_set_name, features in self.all_feature_sets.items():
            all_features.update(features)
        
        merged_list = sorted(list(all_features))
        logging.info(f"✅ 合并后总特征数: {len(merged_list)}个")
        
        return merged_list
    
    def analyze_feature_duplicates(self) -> Dict[str, List[str]]:
        """分析重复特征"""
        logging.info("🔍 分析特征重复...")
        
        feature_count = {}
        feature_sources = {}
        
        # 统计每个特征出现在哪些特征集中
        for set_name, features in self.all_feature_sets.items():
            for feature in features:
                if feature not in feature_count:
                    feature_count[feature] = 0
                    feature_sources[feature] = []
                feature_count[feature] += 1
                feature_sources[feature].append(set_name)
        
        # 找出重复特征
        duplicates = {feature: sources for feature, sources in feature_sources.items() 
                     if len(sources) > 1}
        
        logging.info(f"📊 重复特征分析结果:")
        logging.info(f"  - 总特征数: {len(feature_count)}")
        logging.info(f"  - 重复特征数: {len(duplicates)}")
        logging.info(f"  - 唯一特征数: {len(feature_count) - len(duplicates)}")
        
        # 显示重复最多的特征
        sorted_duplicates = sorted(duplicates.items(), 
                                 key=lambda x: len(x[1]), reverse=True)
        
        logging.info(f"\n🔍 重复最多的特征 (Top 10):")
        for i, (feature, sources) in enumerate(sorted_duplicates[:10], 1):
            logging.info(f"  {i:2d}. {feature:30s}: 出现在{len(sources)}个特征集中")
            logging.info(f"      来源: {', '.join(sources)}")
        
        self.analysis_results['duplicates'] = duplicates
        return duplicates

    def detect_potential_data_leakage(self) -> Dict[str, List[str]]:
        """检测潜在的数据泄漏特征"""
        logging.info("🔍 检测潜在数据泄漏特征...")

        # 定义可能存在数据泄漏的特征模式
        leakage_patterns = {
            'future_looking': [  # 未来信息泄漏
                'future_', 'next_', 'tomorrow_', '明日', '未来', '后续'
            ],
            'target_related': [  # 与目标变量过于相关
                'limit_up', '涨停', 'target', 'label', '标签'
            ],
            'perfect_predictors': [  # 完美预测器（可能是计算错误）
                'perfect_', 'exact_', '精确', '完美'
            ],
            'post_market': [  # 盘后信息
                'after_market', 'post_', '盘后', '收盘后'
            ]
        }

        suspicious_features = {}

        for pattern_type, patterns in leakage_patterns.items():
            suspicious = []
            for feature in self.merged_features:
                feature_lower = feature.lower()
                if any(pattern in feature_lower for pattern in patterns):
                    suspicious.append(feature)

            if suspicious:
                suspicious_features[pattern_type] = suspicious
                logging.warning(f"⚠️ 发现{len(suspicious)}个{pattern_type}可疑特征:")
                for feat in suspicious[:5]:  # 只显示前5个
                    logging.warning(f"    - {feat}")
                if len(suspicious) > 5:
                    logging.warning(f"    ... 还有{len(suspicious)-5}个")

        self.analysis_results['data_leakage'] = suspicious_features
        return suspicious_features

    def analyze_feature_noise(self, df: pd.DataFrame = None) -> Dict[str, any]:
        """分析特征噪声（如果有数据的话）"""
        logging.info("🔍 分析特征噪声...")

        if df is None:
            logging.warning("⚠️ 没有提供数据，跳过噪声分析")
            return {}

        noise_analysis = {}

        # 检查可用特征
        available_features = [f for f in self.merged_features if f in df.columns]
        logging.info(f"📊 可分析特征数: {len(available_features)}")

        if len(available_features) == 0:
            logging.warning("⚠️ 没有可分析的特征")
            return {}

        # 1. 常数特征检测
        constant_features = []
        for feature in available_features:
            if df[feature].nunique() <= 1:
                constant_features.append(feature)

        # 2. 低方差特征检测
        low_variance_features = []
        for feature in available_features:
            if df[feature].dtype in ['float64', 'int64', 'float32', 'int32']:
                variance = df[feature].var()
                if variance < 1e-6:  # 极低方差
                    low_variance_features.append((feature, variance))

        # 3. 高缺失率特征检测
        high_missing_features = []
        for feature in available_features:
            missing_rate = df[feature].isnull().sum() / len(df)
            if missing_rate > 0.8:  # 缺失率超过80%
                high_missing_features.append((feature, missing_rate))

        # 4. 异常值特征检测
        outlier_features = []
        for feature in available_features:
            if df[feature].dtype in ['float64', 'int64', 'float32', 'int32']:
                q1 = df[feature].quantile(0.25)
                q3 = df[feature].quantile(0.75)
                iqr = q3 - q1
                if iqr > 0:
                    outlier_count = ((df[feature] < (q1 - 1.5 * iqr)) |
                                   (df[feature] > (q3 + 1.5 * iqr))).sum()
                    outlier_rate = outlier_count / len(df)
                    if outlier_rate > 0.1:  # 异常值超过10%
                        outlier_features.append((feature, outlier_rate))

        noise_analysis = {
            'constant_features': constant_features,
            'low_variance_features': low_variance_features,
            'high_missing_features': high_missing_features,
            'outlier_features': outlier_features
        }

        # 记录结果
        logging.info(f"📊 噪声分析结果:")
        logging.info(f"  - 常数特征: {len(constant_features)}个")
        logging.info(f"  - 低方差特征: {len(low_variance_features)}个")
        logging.info(f"  - 高缺失特征: {len(high_missing_features)}个")
        logging.info(f"  - 高异常值特征: {len(outlier_features)}个")

        self.analysis_results['noise'] = noise_analysis
        return noise_analysis

    def generate_clean_feature_set(self) -> List[str]:
        """生成清洁的特征集"""
        logging.info("🧹 生成清洁特征集...")

        clean_features = self.merged_features.copy()
        removed_count = 0

        # 移除可疑的数据泄漏特征
        if 'data_leakage' in self.analysis_results:
            for pattern_type, suspicious_features in self.analysis_results['data_leakage'].items():
                for feature in suspicious_features:
                    if feature in clean_features:
                        clean_features.remove(feature)
                        removed_count += 1
                        logging.info(f"🗑️ 移除数据泄漏特征: {feature} ({pattern_type})")

        # 移除噪声特征
        if 'noise' in self.analysis_results:
            noise_data = self.analysis_results['noise']

            # 移除常数特征
            for feature in noise_data.get('constant_features', []):
                if feature in clean_features:
                    clean_features.remove(feature)
                    removed_count += 1
                    logging.info(f"🗑️ 移除常数特征: {feature}")

            # 移除低方差特征
            for feature, variance in noise_data.get('low_variance_features', []):
                if feature in clean_features:
                    clean_features.remove(feature)
                    removed_count += 1
                    logging.info(f"🗑️ 移除低方差特征: {feature} (方差={variance:.2e})")

        logging.info(f"✅ 清洁特征集生成完成:")
        logging.info(f"  - 原始特征数: {len(self.merged_features)}")
        logging.info(f"  - 移除特征数: {removed_count}")
        logging.info(f"  - 清洁特征数: {len(clean_features)}")

        return clean_features

    def run_full_analysis(self, df: pd.DataFrame = None) -> Dict[str, any]:
        """运行完整分析"""
        logging.info("🚀 开始全面特征分析...")

        # 1. 重复特征分析
        self.analyze_feature_duplicates()

        # 2. 数据泄漏检测
        self.detect_potential_data_leakage()

        # 3. 噪声分析（如果有数据）
        if df is not None:
            self.analyze_feature_noise(df)

        # 4. 生成清洁特征集
        clean_features = self.generate_clean_feature_set()

        # 5. 生成报告
        report = {
            'total_features': len(self.merged_features),
            'feature_sets_count': len(self.all_feature_sets),
            'duplicates_count': len(self.analysis_results.get('duplicates', {})),
            'leakage_features_count': sum(len(v) for v in self.analysis_results.get('data_leakage', {}).values()),
            'clean_features_count': len(clean_features),
            'clean_features': clean_features,
            'all_feature_sets': self.all_feature_sets,
            'analysis_results': self.analysis_results
        }

        logging.info("✅ 全面特征分析完成!")
        logging.info(f"📊 分析报告:")
        logging.info(f"  - 总特征数: {report['total_features']}")
        logging.info(f"  - 特征集数: {report['feature_sets_count']}")
        logging.info(f"  - 重复特征数: {report['duplicates_count']}")
        logging.info(f"  - 可疑泄漏特征数: {report['leakage_features_count']}")
        logging.info(f"  - 清洁特征数: {report['clean_features_count']}")

        return report


if __name__ == "__main__":
    # 创建分析器并运行分析
    analyzer = ComprehensiveFeatureAnalyzer()

    # 运行完整分析（没有数据时只做静态分析）
    report = analyzer.run_full_analysis()

    # 输出清洁特征集
    print("\n" + "="*80)
    print("🎯 推荐的清洁特征集 (前50个):")
    print("="*80)
    for i, feature in enumerate(report['clean_features'][:50], 1):
        print(f"{i:3d}. {feature}")

    if len(report['clean_features']) > 50:
        print(f"... 还有{len(report['clean_features'])-50}个特征")

    print(f"\n总计: {len(report['clean_features'])}个清洁特征")
