#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🎯 应用最终AUC优化补丁到P.py
"""

import logging
import re

def apply_final_patch_to_p():
    """应用最终补丁到P.py"""
    logging.info("🚀 开始应用最终AUC优化补丁到P.py...")
    
    try:
        # 读取P.py
        with open('P.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. 替换get_all_feature_columns函数
        pattern = r'def get_all_feature_columns\(df\):.*?return.*?(?=

def|
class|
#|\Z)'
        replacement = """def get_all_feature_columns(df):
    """🎯 最终优化版：使用17个核心特征"""
    import logging
    
    # 🎯 基于云服务器测试的最优特征集
    OPTIMIZED_FEATURE_SET = [
        'pct_chg', 'change', '主力净流入占比', '冲高回落指标', '真实振幅', '承接力度',
        'open', 'high', 'low', 'close', 'pre_close', 'vol', 'amount', 
        'turnover_rate', 'volume_ratio', 'pe', 'pb'
    ]
    
    logging.info("🎯 使用最终优化特征集（17个核心特征）")
    
    available_features = [f for f in OPTIMIZED_FEATURE_SET if f in df.columns]
    missing_features = [f for f in OPTIMIZED_FEATURE_SET if f not in df.columns]
    
    if missing_features:
        for feature in missing_features:
            if feature not in df.columns:
                df[feature] = 0.0
    
    available_features = [f for f in OPTIMIZED_FEATURE_SET if f in df.columns]
    logging.info(f"✅ 最终优化特征集: {len(available_features)}个特征")
    
    return available_features"""
        
        content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        # 2. 强制启用特征优化
        content = re.sub(r'ENABLE_FEATURE_OPTIMIZATION\s*=\s*False', 
                        'ENABLE_FEATURE_OPTIMIZATION = True  # 🎯 最终优化：强制启用', 
                        content)
        
        # 3. 更新UNIFIED_FEATURE_SET
        pattern = r'UNIFIED_FEATURE_SET\s*=\s*\[.*?\]'
        replacement = """UNIFIED_FEATURE_SET = [
    'pct_chg', 'change', '主力净流入占比', '冲高回落指标', '真实振幅', '承接力度',
    'open', 'high', 'low', 'close', 'pre_close', 'vol', 'amount', 
    'turnover_rate', 'volume_ratio', 'pe', 'pb'
]  # 🎯 最终优化：17个核心特征"""
        
        content = re.sub(pattern, replacement, content, flags=re.DOTALL)
        
        # 保存修改后的文件
        with open('P.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        logging.info("✅ 最终优化补丁应用成功")
        return True
        
    except Exception as e:
        logging.error(f"❌ 补丁应用失败: {e}")
        return False

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    apply_final_patch_to_p()
