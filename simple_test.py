#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 简单测试 - 直接测试get_all_feature_columns_final_fix函数
"""

import pandas as pd
import logging

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_feature_function():
    """测试特征函数"""
    print("🧪 开始测试get_all_feature_columns_final_fix函数...")
    
    try:
        # 导入P模块
        import P
        
        # 创建测试数据
        test_data = {
            'pct_chg': [1.0, 2.0, 3.0],
            'change': [0.1, 0.2, 0.3],
            'open': [10.0, 11.0, 12.0],
            'high': [10.5, 11.5, 12.5],
            'low': [9.5, 10.5, 11.5],
            'close': [10.2, 11.2, 12.2],
            'vol': [1000, 1100, 1200],
            'amount': [10000, 11000, 12000],
            'noise_feature_1': [0.1, 0.2, 0.3],
            'noise_feature_2': [0.4, 0.5, 0.6],
        }
        
        test_df = pd.DataFrame(test_data)
        print(f"📊 测试数据创建: {test_df.shape[1]}列")
        print(f"📊 测试数据列: {list(test_df.columns)}")
        
        # 测试函数
        if hasattr(P, 'get_all_feature_columns_final_fix'):
            print("✅ get_all_feature_columns_final_fix函数存在")
            
            features = P.get_all_feature_columns_final_fix(test_df)
            
            print(f"📊 返回特征数量: {len(features)}")
            print(f"📊 返回特征列表: {features}")
            
            if len(features) == 15:
                print("✅ 特征数量正确 (15个)")
                return True
            else:
                print(f"❌ 特征数量错误: 期望15个，实际{len(features)}个")
                return False
        else:
            print("❌ get_all_feature_columns_final_fix函数不存在")
            return False
            
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_feature_function()
    print(f"\n🎯 测试结果: {'✅ 成功' if success else '❌ 失败'}")
