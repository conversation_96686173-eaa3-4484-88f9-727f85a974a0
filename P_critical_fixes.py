#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 P.py关键修复方案
基于云服务器日志分析的紧急修复

🚨 发现的关键问题：
1. 特征维度不匹配：训练681/687个特征，预测668个特征
2. 反标准化错误：使用通用参数导致所有预测结果相同
3. AUC=0.5：特征不一致导致模型失效
4. 预测多样性缺失：所有股票预测结果几乎相同

🎯 修复目标：
1. 统一特征集，确保训练预测一致
2. 修复反标准化逻辑
3. 恢复模型预测能力
4. 确保预测结果多样性
"""

import logging
import pandas as pd
import numpy as np
import joblib
import os
from typing import List, Dict, Any

# 🎯 统一特征集定义（基于日志分析的重要特征）
CRITICAL_FEATURES = [
    # 🏆 Top重要特征（基于日志特征重要性分析）
    'pct_chg',           # 0.363540 - 最重要特征
    '真实振幅',           # 0.172434 - 第二重要
    '承接力度',           # 0.044347 - 第三重要
    'wr_14',             # 0.031342 - 第四重要
    '冲高回落指标',       # 0.028128 - 第五重要
    'change',            # 0.020624 - 第六重要
    'is_on_ths_hot',     # 0.018848 - 第七重要
    'open',              # 0.018371 - 第八重要
    '分时走势强度',       # 0.017696 - 第九重要
    '主力净流入占比',     # 0.014523 - 第十重要
    
    # 📊 基础价格特征
    'high', 'low', 'close', 'pre_close', 'vol', 'amount',
    
    # 📈 技术指标特征
    'rsi2', 'cci', 'mfi', '板块联动强度', 'market_pct_std',
    '资金技术共振', '振幅', 'GK波动率', '日内高低比', '竞价异动',
    
    # 💰 成交量特征
    'turnover_rate', 'turnover_rate_f', 'volume_ratio',
    
    # 📋 估值特征
    'pe', 'pe_ttm', 'pb', 'total_share', 'float_share'
]

def get_all_feature_columns_fixed(df):
    """
    🔧 修复版：获取所有特征列
    强制使用CRITICAL_FEATURES，确保训练预测一致性
    """
    logging.info("🔧 使用修复版特征获取函数")
    
    # 🎯 强制使用关键特征集
    available_features = [f for f in CRITICAL_FEATURES if f in df.columns]
    missing_features = [f for f in CRITICAL_FEATURES if f not in df.columns]
    
    if missing_features:
        logging.warning(f"⚠️ 缺失关键特征: {missing_features[:5]}...")
        
        # 🔧 为缺失特征创建默认值
        for feature in missing_features:
            if feature not in df.columns:
                df[feature] = 0.0  # 使用默认值填充
                logging.info(f"  ✅ 已填充缺失特征: {feature}")
        
        # 重新检查可用特征
        available_features = [f for f in CRITICAL_FEATURES if f in df.columns]
    
    logging.info(f"✅ 修复版特征获取: {len(available_features)}个特征（目标: {len(CRITICAL_FEATURES)}个）")
    return available_features

def denormalize_predictions_fixed(predictions, strategy_type="首板"):
    """
    🔧 修复版反标准化函数
    使用训练时保存的特定标准化参数，而非通用参数
    """
    import joblib
    import numpy as np
    import logging
    
    try:
        # 🎯 方法1：尝试加载训练时保存的标准化器
        scaler_path = f"models/{strategy_type}_scaler.joblib"
        
        if os.path.exists(scaler_path):
            scaler = joblib.load(scaler_path)
            logging.info(f"✅ 成功加载{strategy_type}策略标准化器")
            
            # 使用正确的标准化器进行反标准化
            if hasattr(scaler, 'inverse_transform'):
                denormalized = scaler.inverse_transform(predictions.reshape(-1, 1)).flatten()
                logging.info(f"✅ 使用训练时标准化参数反标准化: {strategy_type}")
                return denormalized
        
        # 🎯 方法2：使用日志中记录的特定参数
        logging.warning(f"⚠️ 标准化器文件不存在，使用日志参数: {scaler_path}")
        
        if strategy_type == "首板":
            # 基于日志: 保存首板策略MAIN市场regression_output_1标准化参数: median=1.0000, iqr=19.2948
            median, iqr = 1.0000, 19.2948
        elif strategy_type == "连板":
            # 基于日志: 保存连板策略MAIN市场regression_output_1标准化参数: median=1.0000, iqr=15.4950
            median, iqr = 1.0000, 15.4950
        else:
            median, iqr = 1.0000, 10.0000
        
        # 🔧 修复反标准化公式
        # 标准化: normalized = (value - median) / iqr
        # 反标准化: value = normalized * iqr + median
        denormalized = predictions * iqr + median
        
        logging.info(f"✅ 使用日志参数反标准化: {strategy_type}, median={median}, iqr={iqr}")
        logging.info(f"📊 反标准化结果范围: [{denormalized.min():.4f}, {denormalized.max():.4f}]")
        
        return denormalized
        
    except Exception as e:
        logging.error(f"❌ 反标准化失败: {e}")
        logging.warning("⚠️ 返回原始预测值")
        return predictions

def validate_prediction_diversity(predictions, stock_codes, strategy_type="首板"):
    """
    🔧 验证预测结果多样性
    确保不同股票有不同的预测结果
    """
    logging.info(f"🔍 验证{strategy_type}策略预测多样性...")
    
    try:
        # 计算预测结果的统计信息
        pred_mean = np.mean(predictions)
        pred_std = np.std(predictions)
        pred_min = np.min(predictions)
        pred_max = np.max(predictions)
        unique_count = len(np.unique(predictions))
        
        logging.info(f"📊 预测结果统计:")
        logging.info(f"  - 平均值: {pred_mean:.4f}")
        logging.info(f"  - 标准差: {pred_std:.4f}")
        logging.info(f"  - 范围: [{pred_min:.4f}, {pred_max:.4f}]")
        logging.info(f"  - 唯一值数量: {unique_count}/{len(predictions)}")
        
        # 🚨 检查多样性问题
        diversity_issues = []
        
        if pred_std < 0.01:
            diversity_issues.append(f"标准差过小({pred_std:.6f})，预测结果过于一致")
        
        if unique_count < len(predictions) * 0.1:
            diversity_issues.append(f"唯一值过少({unique_count}/{len(predictions)})，缺乏多样性")
        
        if abs(pred_max - pred_min) < 0.1:
            diversity_issues.append(f"预测范围过小({pred_max - pred_min:.4f})，可能存在问题")
        
        if diversity_issues:
            logging.error(f"❌ {strategy_type}策略预测多样性问题:")
            for issue in diversity_issues:
                logging.error(f"  - {issue}")
            return False
        else:
            logging.info(f"✅ {strategy_type}策略预测多样性正常")
            return True
            
    except Exception as e:
        logging.error(f"❌ 多样性验证失败: {e}")
        return False

def fix_feature_dimension_mismatch():
    """
    🔧 修复特征维度不匹配问题
    确保训练和预测使用相同数量的特征
    """
    logging.info("🔧 修复特征维度不匹配问题...")
    
    fix_instructions = f"""
# 🔧 P.py关键修复指令

## 1. 替换get_all_feature_columns函数
将P.py中的get_all_feature_columns函数替换为：
{get_all_feature_columns_fixed.__doc__}

## 2. 修复反标准化函数
在predict_and_select函数中，将反标准化部分替换为：
{denormalize_predictions_fixed.__doc__}

## 3. 添加预测多样性验证
在预测完成后添加：
{validate_prediction_diversity.__doc__}

## 4. 统一特征集
确保所有地方都使用CRITICAL_FEATURES：
- 特征数量: {len(CRITICAL_FEATURES)}个
- 核心特征: {CRITICAL_FEATURES[:10]}...

## 5. 验证步骤
1. 检查训练时特征数量是否为{len(CRITICAL_FEATURES)}个
2. 检查预测时特征数量是否为{len(CRITICAL_FEATURES)}个
3. 验证反标准化结果的合理性
4. 确认预测结果的多样性
"""
    
    print(fix_instructions)
    return fix_instructions

def main():
    """主函数"""
    logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')
    
    print("🚀 P.py关键修复方案")
    print("="*80)
    
    print(f"\n🎯 统一特征集: {len(CRITICAL_FEATURES)}个关键特征")
    print("📊 基于日志分析的Top重要特征:")
    for i, feature in enumerate(CRITICAL_FEATURES[:10], 1):
        print(f"  {i:2d}. {feature}")
    
    print("\n🔧 修复指令:")
    fix_feature_dimension_mismatch()
    
    print("\n✅ 修复完成后预期效果:")
    print("  1. AUC回归到真实水平（不再虚高或过低）")
    print("  2. 预测结果具有多样性（不同股票不同预测）")
    print("  3. 反标准化结果合理（涨幅在合理范围内）")
    print("  4. 特征维度完全一致（训练预测相同）")

if __name__ == "__main__":
    main()
