#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 综合特征分析器V2 - 系统性分析600+个动态特征
基于日志分析和P.py源码，深度清理特征系统

🚨 核心问题：
1. 特征膨胀：292个 → 595个额外特征
2. 缺失特征：35个重要特征持续缺失  
3. 重复特征：大量_quality, _score, _confidence后缀特征
4. AUC下降：从0.7263降到0.5000

🎯 解决策略：
1. 从tushare数据源头开始分析
2. 逐个测试600+个特征的有效性
3. 删除重复和噪声特征
4. 构建最优特征集
"""

import pandas as pd
import numpy as np
import logging
import json
import re
from typing import List, Dict, Set, Tuple
import warnings
warnings.filterwarnings('ignore')

logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class AdvancedFeatureAnalyzer:
    """高级特征分析器"""
    
    def __init__(self):
        self.logger = logging.getLogger(__name__)
        
        # 🎯 tushare原始特征（数据源头）
        self.tushare_base = [
            'ts_code', 'trade_date', 'open', 'high', 'low', 'close', 'pre_close',
            'change', 'pct_chg', 'vol', 'amount', 'adj_factor',
            'turnover_rate', 'turnover_rate_f', 'volume_ratio', 'pe', 'pe_ttm',
            'pb', 'ps', 'ps_ttm', 'total_share', 'float_share', 'free_share',
            'total_mv', 'circ_mv'
        ]
        
        # 🚨 持续缺失的35个特征
        self.missing_critical = [
            'obv', '大单净流入', '中单净流入', '小单净流入', '超大单净流入', '机构净流入',
            'market_sentiment', 'market_volatility', '板块平均涨幅', '板块资金流入',
            'sector_momentum', 'sector_strength', 'sector_hot_rank', 'weight_avg', 'winner_rate',
            'cost_5pct', 'cost_15pct', 'cost_50pct', 'cost_85pct', 'cost_95pct',
            'weight_avg_change', 'winner_rate_change', 'cost_spread', 'cost_concentration',
            'cost_pressure', '筹码松散度', '量价配合度', '技术指标合成信号', '热点技术共振',
            '首板市场适应度', '连板持续概率', 'year', 'is_month_end', 'is_quarter_end', 'is_year_end'
        ]
        
        self.analysis_results = {}
        
    def extract_features_from_log(self, log_file='stock_prediction_20250731_192453.log'):
        """从日志中提取所有动态特征"""
        self.logger.info("📊 从日志提取600+个动态特征...")
        
        try:
            with open(log_file, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取额外特征列表
            all_features = set()
            
            # 方法1: 提取额外特征列表
            pattern1 = r"额外特征 \((\d+)个\): \[(.*?)\]"
            matches1 = re.findall(pattern1, content, re.DOTALL)
            
            for count, features_str in matches1:
                self.logger.info(f"发现额外特征组: {count}个")
                # 清理和解析特征字符串
                features_str = features_str.replace("'", "").replace('"', '')
                features = [f.strip() for f in features_str.split(',') if f.strip() and '...' not in f]
                all_features.update(features)
            
            # 方法2: 提取缺失特征列表
            pattern2 = r"仍然缺失的特征: \[(.*?)\]"
            matches2 = re.findall(pattern2, content, re.DOTALL)
            
            for features_str in matches2:
                features_str = features_str.replace("'", "").replace('"', '')
                features = [f.strip() for f in features_str.split(',') if f.strip()]
                all_features.update(features)
            
            # 方法3: 提取稀疏特征
            pattern3 = r"(\w+(?:_\w+)*) \(缺失\d+\.\d+%\)"
            matches3 = re.findall(pattern3, content)
            all_features.update(matches3)
            
            self.logger.info(f"✅ 从日志提取特征总数: {len(all_features)}")
            return list(all_features)
            
        except Exception as e:
            self.logger.error(f"❌ 日志特征提取失败: {e}")
            return []
    
    def analyze_feature_patterns(self, features: List[str]) -> Dict[str, List[str]]:
        """分析特征模式和分类"""
        self.logger.info("🔍 分析特征模式...")
        
        patterns = {
            'quality_suffix': [],      # _quality后缀
            'score_suffix': [],        # _score后缀  
            'confidence_suffix': [],   # _confidence后缀
            'redundant_suffix': [],    # _is_redundant后缀
            'valid_suffix': [],        # _valid后缀
            'strength_suffix': [],     # _strength后缀
            'chinese_features': [],    # 中文特征
            'technical_indicators': [],# 技术指标
            'time_features': [],       # 时间特征
            'volume_features': [],     # 成交量特征
            'price_features': [],      # 价格特征
            'market_features': [],     # 市场特征
            'unknown_features': []     # 未知特征
        }
        
        for feature in features:
            feature_lower = feature.lower()
            
            # 按后缀分类
            if feature.endswith('_quality'):
                patterns['quality_suffix'].append(feature)
            elif feature.endswith('_score'):
                patterns['score_suffix'].append(feature)
            elif feature.endswith('_confidence'):
                patterns['confidence_suffix'].append(feature)
            elif feature.endswith('_is_redundant'):
                patterns['redundant_suffix'].append(feature)
            elif feature.endswith('_valid'):
                patterns['valid_suffix'].append(feature)
            elif feature.endswith('_strength'):
                patterns['strength_suffix'].append(feature)
            
            # 按内容分类
            elif any('\u4e00' <= char <= '\u9fff' for char in feature):
                patterns['chinese_features'].append(feature)
            elif any(x in feature_lower for x in ['rsi', 'macd', 'cci', 'kdj', 'ma', 'ema', 'boll']):
                patterns['technical_indicators'].append(feature)
            elif any(x in feature_lower for x in ['year', 'month', 'day', 'quarter', 'week']):
                patterns['time_features'].append(feature)
            elif any(x in feature_lower for x in ['vol', 'amount', 'turnover', 'volume']):
                patterns['volume_features'].append(feature)
            elif any(x in feature_lower for x in ['open', 'high', 'low', 'close', 'pct_chg', 'change']):
                patterns['price_features'].append(feature)
            elif any(x in feature_lower for x in ['market', 'sector', 'industry']):
                patterns['market_features'].append(feature)
            else:
                patterns['unknown_features'].append(feature)
        
        # 输出分析结果
        for pattern, feature_list in patterns.items():
            if feature_list:
                self.logger.info(f"📊 {pattern}: {len(feature_list)}个")
                if len(feature_list) <= 5:
                    self.logger.info(f"    {feature_list}")
                else:
                    self.logger.info(f"    {feature_list[:3]}... (显示前3个)")
        
        return patterns
    
    def detect_redundant_features(self, patterns: Dict[str, List[str]]) -> Dict[str, List[str]]:
        """检测冗余特征"""
        self.logger.info("🔍 检测冗余特征...")
        
        redundant_groups = {}
        
        # 1. 检测后缀冗余
        suffix_groups = ['quality_suffix', 'score_suffix', 'confidence_suffix', 'redundant_suffix', 'valid_suffix']
        for suffix_type in suffix_groups:
            features = patterns[suffix_type]
            base_names = {}
            
            for feature in features:
                # 提取基础名称
                if '_quality' in feature:
                    base_name = feature.replace('_quality', '')
                elif '_score' in feature:
                    base_name = feature.replace('_score', '')
                elif '_confidence' in feature:
                    base_name = feature.replace('_confidence', '')
                elif '_is_redundant' in feature:
                    base_name = feature.replace('_is_redundant', '')
                elif '_valid' in feature:
                    base_name = feature.replace('_valid', '')
                else:
                    continue
                
                if base_name not in base_names:
                    base_names[base_name] = []
                base_names[base_name].append(feature)
            
            # 找出有多个变体的基础名称
            for base_name, variants in base_names.items():
                if len(variants) > 1:
                    redundant_groups[f"{suffix_type}_{base_name}"] = variants
        
        # 2. 检测相似名称
        all_features = []
        for feature_list in patterns.values():
            all_features.extend(feature_list)
        
        similar_groups = {}
        for i, feature1 in enumerate(all_features):
            for j, feature2 in enumerate(all_features[i+1:], i+1):
                # 计算相似度
                similarity = self.calculate_feature_similarity(feature1, feature2)
                if similarity > 0.8:  # 80%相似度
                    key = f"similar_{min(i,j)}"
                    if key not in similar_groups:
                        similar_groups[key] = []
                    similar_groups[key].extend([feature1, feature2])
        
        # 合并结果
        redundant_groups.update(similar_groups)
        
        self.logger.info(f"🔍 发现冗余特征组: {len(redundant_groups)}组")
        for group_name, features in list(redundant_groups.items())[:5]:
            self.logger.info(f"  {group_name}: {features}")
        
        return redundant_groups
    
    def calculate_feature_similarity(self, feature1: str, feature2: str) -> float:
        """计算特征相似度"""
        # 简单的字符串相似度计算
        if feature1 == feature2:
            return 1.0
        
        # 去除常见后缀进行比较
        suffixes = ['_quality', '_score', '_confidence', '_is_redundant', '_valid', '_strength']
        clean1, clean2 = feature1, feature2
        
        for suffix in suffixes:
            clean1 = clean1.replace(suffix, '')
            clean2 = clean2.replace(suffix, '')
        
        if clean1 == clean2:
            return 0.9
        
        # 计算编辑距离相似度
        max_len = max(len(clean1), len(clean2))
        if max_len == 0:
            return 1.0
        
        # 简化的相似度计算
        common_chars = sum(1 for c1, c2 in zip(clean1, clean2) if c1 == c2)
        return common_chars / max_len
    
    def prioritize_features(self, patterns: Dict[str, List[str]], redundant_groups: Dict[str, List[str]]) -> List[str]:
        """特征优先级排序"""
        self.logger.info("🎯 特征优先级排序...")
        
        priority_features = []
        
        # 1. 最高优先级：tushare基础特征
        priority_features.extend([f for f in self.tushare_base if f in patterns.get('price_features', []) + patterns.get('volume_features', [])])
        
        # 2. 高优先级：重要的中文自定义特征
        important_chinese = ['真实振幅', '承接力度', '冲高回落指标', '主力净流入占比', '分时走势强度']
        priority_features.extend([f for f in important_chinese if f in patterns.get('chinese_features', [])])
        
        # 3. 中优先级：核心技术指标（去重）
        tech_features = patterns.get('technical_indicators', [])
        core_tech = ['rsi2', 'rsi6', 'rsi14', 'macd', 'cci', 'wr_14', 'mfi']
        for tech in core_tech:
            matching = [f for f in tech_features if tech in f.lower() and not any(suffix in f for suffix in ['_quality', '_score', '_confidence'])]
            if matching:
                priority_features.append(matching[0])  # 只取第一个
        
        # 4. 低优先级：其他有用特征
        other_useful = patterns.get('market_features', [])[:5]  # 最多5个市场特征
        priority_features.extend(other_useful)
        
        # 去重
        final_priority = []
        for feature in priority_features:
            if feature not in final_priority:
                final_priority.append(feature)
        
        self.logger.info(f"✅ 优先级特征排序完成: {len(final_priority)}个")
        return final_priority
    
    def generate_optimized_feature_set(self, priority_features: List[str]) -> List[str]:
        """生成优化特征集"""
        self.logger.info("🧹 生成优化特征集...")
        
        # 确保包含核心基础特征
        core_features = [
            'pct_chg', 'change', 'open', 'high', 'low', 'close', 'pre_close',
            'vol', 'amount', 'turnover_rate', 'volume_ratio', 'pe', 'pb'
        ]
        
        optimized_set = []
        
        # 添加核心特征
        for feature in core_features:
            if feature not in optimized_set:
                optimized_set.append(feature)
        
        # 添加优先级特征（避免重复）
        for feature in priority_features:
            if feature not in optimized_set and len(optimized_set) < 50:  # 限制在50个以内
                optimized_set.append(feature)
        
        self.logger.info(f"✅ 优化特征集生成完成: {len(optimized_set)}个特征")
        return optimized_set
    
    def save_comprehensive_analysis(self, patterns, redundant_groups, priority_features, optimized_set):
        """保存综合分析结果"""
        results = {
            'summary': {
                'total_features_analyzed': sum(len(features) for features in patterns.values()),
                'redundant_groups_found': len(redundant_groups),
                'priority_features_count': len(priority_features),
                'optimized_set_count': len(optimized_set),
                'missing_critical_count': len(self.missing_critical)
            },
            'feature_patterns': {k: v for k, v in patterns.items() if v},
            'redundant_groups': redundant_groups,
            'priority_features': priority_features,
            'optimized_feature_set': optimized_set,
            'missing_critical_features': self.missing_critical,
            'recommendations': {
                'remove_redundant': sum(len(group) - 1 for group in redundant_groups.values()),
                'add_missing': len(self.missing_critical),
                'final_target_count': len(optimized_set)
            }
        }
        
        with open('comprehensive_feature_analysis_v2.json', 'w', encoding='utf-8') as f:
            json.dump(results, f, ensure_ascii=False, indent=2)
        
        self.logger.info("💾 综合分析结果已保存")
        return results

def main():
    """主函数"""
    logging.info("🚀 启动高级特征分析器V2...")
    
    analyzer = AdvancedFeatureAnalyzer()
    
    # 1. 从日志提取所有特征
    all_features = analyzer.extract_features_from_log()
    
    if not all_features:
        logging.error("❌ 无法从日志提取特征，退出分析")
        return
    
    # 2. 分析特征模式
    patterns = analyzer.analyze_feature_patterns(all_features)
    
    # 3. 检测冗余特征
    redundant_groups = analyzer.detect_redundant_features(patterns)
    
    # 4. 特征优先级排序
    priority_features = analyzer.prioritize_features(patterns, redundant_groups)
    
    # 5. 生成优化特征集
    optimized_set = analyzer.generate_optimized_feature_set(priority_features)
    
    # 6. 保存分析结果
    results = analyzer.save_comprehensive_analysis(patterns, redundant_groups, priority_features, optimized_set)
    
    # 7. 输出报告
    print("\n" + "="*80)
    print("🔍 高级特征分析报告V2")
    print("="*80)
    
    print(f"\n📊 分析统计:")
    print(f"  - 总分析特征: {results['summary']['total_features_analyzed']}个")
    print(f"  - 冗余特征组: {results['summary']['redundant_groups_found']}组")
    print(f"  - 优先级特征: {results['summary']['priority_features_count']}个")
    print(f"  - 优化特征集: {results['summary']['optimized_set_count']}个")
    print(f"  - 缺失关键特征: {results['summary']['missing_critical_count']}个")
    
    print(f"\n✅ 推荐的优化特征集:")
    for i, feature in enumerate(optimized_set, 1):
        print(f"  {i:2d}. {feature}")
    
    print(f"\n🎯 优化建议:")
    print(f"  - 删除冗余特征: {results['recommendations']['remove_redundant']}个")
    print(f"  - 补充缺失特征: {results['recommendations']['add_missing']}个")
    print(f"  - 目标特征数量: {results['recommendations']['final_target_count']}个")
    
    print(f"\n🚀 下一步:")
    print(f"  1. 应用优化特征集到P.py")
    print(f"  2. 删除冗余和无效特征")
    print(f"  3. 重新训练验证AUC改善")
    
    logging.info("✅ 高级特征分析V2完成")

if __name__ == "__main__":
    main()
