#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 特征清理验证脚本
验证P.py中的特征清理效果
"""

import logging
import sys

def verify_feature_cleanup():
    """验证特征清理效果"""
    logging.basicConfig(level=logging.INFO)
    logging.info("🧪 开始验证特征清理效果...")
    
    try:
        # 导入清理后的P模块
        import P
        
        # 验证清理标记
        if hasattr(P, 'FEATURE_CLEANUP_APPLIED'):
            logging.info(f"✅ 特征清理标记存在: {P.FEATURE_CLEANUP_APPLIED}")
        else:
            logging.error("❌ 缺少特征清理标记")
        
        if hasattr(P, 'validate_feature_cleanup'):
            P.validate_feature_cleanup()
        
        # 验证核心特征集
        if hasattr(P, 'UNIFIED_FEATURE_SET'):
            feature_count = len(P.UNIFIED_FEATURE_SET)
            logging.info(f"📊 UNIFIED_FEATURE_SET: {feature_count}个特征")
            if feature_count == 15:
                logging.info("✅ 特征数量正确")
            else:
                logging.warning(f"⚠️ 特征数量异常，期望15个，实际{feature_count}个")
        
        # 测试特征获取
        import pandas as pd
        test_data = {f: [1, 2, 3] for f in ['pct_chg', 'change', 'open', 'close', 'vol']}
        test_df = pd.DataFrame(test_data)
        
        if hasattr(P, 'get_all_feature_columns'):
            features = P.get_all_feature_columns(test_df)
            logging.info(f"📊 获取特征测试: {len(features)}个特征")
            logging.info(f"📊 特征列表: {features}")
            
            if len(features) <= 20:  # 应该在15个左右
                logging.info("✅ 特征获取测试通过")
                return True
            else:
                logging.error(f"❌ 特征数量过多: {len(features)}")
                return False
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 验证失败: {e}")
        return False

if __name__ == "__main__":
    success = verify_feature_cleanup()
    sys.exit(0 if success else 1)
