#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 应用关键修复到P.py
基于深度分析的紧急修复方案

🚨 发现的根本问题：
1. ENABLE_FEATURE_OPTIMIZATION可能未启用，导致特征数量不一致
2. NORMALIZATION_PARAMS缺少正确参数，导致反标准化错误
3. 训练和预测使用不同的特征模式

🎯 修复策略：
1. 强制启用特征优化模式
2. 修复标准化参数
3. 确保特征一致性
4. 验证预测多样性
"""

import logging
import os
import re

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def apply_critical_fixes_to_p():
    """应用关键修复到P.py"""
    logging.info("🚀 开始应用关键修复到P.py...")
    
    try:
        # 读取P.py文件
        with open('P.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        logging.info(f"📊 P.py文件大小: {len(content)} 字符")
        
        # 🔧 修复1: 强制启用特征优化模式
        logging.info("🔧 修复1: 强制启用特征优化模式...")
        
        # 查找ENABLE_FEATURE_OPTIMIZATION的定义
        pattern1 = r'ENABLE_FEATURE_OPTIMIZATION\s*=\s*False'
        if re.search(pattern1, content):
            content = re.sub(pattern1, 'ENABLE_FEATURE_OPTIMIZATION = True', content)
            logging.info("✅ 已强制启用特征优化模式")
        else:
            # 如果没找到False，检查是否已经是True
            pattern1_check = r'ENABLE_FEATURE_OPTIMIZATION\s*=\s*True'
            if re.search(pattern1_check, content):
                logging.info("✅ 特征优化模式已启用")
            else:
                # 如果都没找到，在文件开头添加
                content = "ENABLE_FEATURE_OPTIMIZATION = True  # 🔧 强制启用特征优化\n\n" + content
                logging.info("✅ 已添加特征优化模式启用")
        
        # 🔧 修复2: 修复标准化参数初始化
        logging.info("🔧 修复2: 修复标准化参数初始化...")
        
        # 查找NORMALIZATION_PARAMS的定义并确保包含正确的参数
        normalization_fix = '''
# 🔧 修复：确保标准化参数正确初始化
if 'NORMALIZATION_PARAMS' not in globals():
    NORMALIZATION_PARAMS = {}

# 🔧 基于日志分析的正确标准化参数
NORMALIZATION_PARAMS.update({
    '首板': {
        'MAIN': {
            'regression_output_1': {'median': 1.0000, 'iqr': 19.2948, 'clip_range': 15.0},
            'regression_output_2': {'median': 1.0000, 'iqr': 8.0000, 'clip_range': 15.0}
        }
    },
    '连板': {
        'MAIN': {
            'regression_output_1': {'median': 1.0000, 'iqr': 15.4950, 'clip_range': 15.0},
            'regression_output_2': {'median': 1.0000, 'iqr': 15.3938, 'clip_range': 15.0}
        }
    }
})

logging.info("✅ 标准化参数已修复初始化")
'''
        
        # 在import语句后添加标准化参数修复
        import_pattern = r'(import\s+.*?\n\n)'
        if re.search(import_pattern, content, re.DOTALL):
            content = re.sub(import_pattern, r'\1' + normalization_fix + '\n', content, count=1)
            logging.info("✅ 已添加标准化参数修复")
        
        # 🔧 修复3: 添加特征一致性检查
        logging.info("🔧 修复3: 添加特征一致性检查...")
        
        feature_check_code = '''
def ensure_feature_consistency_fixed():
    """🔧 确保特征一致性的修复函数"""
    global ENABLE_FEATURE_OPTIMIZATION, UNIFIED_FEATURE_SET
    
    if not ENABLE_FEATURE_OPTIMIZATION:
        logging.warning("⚠️ 特征优化模式未启用，强制启用...")
        ENABLE_FEATURE_OPTIMIZATION = True
    
    if len(UNIFIED_FEATURE_SET) != 136:
        logging.error(f"❌ UNIFIED_FEATURE_SET数量异常: {len(UNIFIED_FEATURE_SET)}，应为136")
    else:
        logging.info(f"✅ UNIFIED_FEATURE_SET数量正确: {len(UNIFIED_FEATURE_SET)}")
    
    return ENABLE_FEATURE_OPTIMIZATION

# 🔧 在关键位置调用特征一致性检查
'''
        
        # 在get_all_feature_columns函数前添加检查函数
        get_all_pattern = r'(def get_all_feature_columns\(df\):)'
        content = re.sub(get_all_pattern, feature_check_code + r'\n\1', content)
        logging.info("✅ 已添加特征一致性检查")
        
        # 🔧 修复4: 在get_all_feature_columns函数开头添加检查调用
        logging.info("🔧 修复4: 在特征获取函数中添加检查...")
        
        # 在get_all_feature_columns函数开头添加检查调用
        func_start_pattern = r'(def get_all_feature_columns\(df\):\s*""".*?"""\s*)'
        func_fix = r'\1    ensure_feature_consistency_fixed()  # 🔧 确保特征一致性\n    '
        content = re.sub(func_start_pattern, func_fix, content, flags=re.DOTALL)
        
        # 🔧 修复5: 添加预测多样性验证
        logging.info("🔧 修复5: 添加预测多样性验证...")
        
        diversity_check = '''
def validate_prediction_diversity_fixed(predictions, stock_codes, strategy_type="首板"):
    """🔧 验证预测结果多样性"""
    import numpy as np
    
    try:
        pred_std = np.std(predictions)
        unique_count = len(np.unique(predictions))
        pred_range = np.max(predictions) - np.min(predictions)
        
        logging.info(f"📊 {strategy_type}策略预测多样性:")
        logging.info(f"  - 标准差: {pred_std:.6f}")
        logging.info(f"  - 唯一值: {unique_count}/{len(predictions)}")
        logging.info(f"  - 范围: {pred_range:.6f}")
        
        # 检查多样性问题
        if pred_std < 0.01:
            logging.error(f"❌ {strategy_type}策略预测标准差过小: {pred_std:.6f}")
            return False
        
        if unique_count < len(predictions) * 0.1:
            logging.error(f"❌ {strategy_type}策略预测唯一值过少: {unique_count}/{len(predictions)}")
            return False
        
        logging.info(f"✅ {strategy_type}策略预测多样性正常")
        return True
        
    except Exception as e:
        logging.error(f"❌ 多样性验证失败: {e}")
        return False

'''
        
        # 在文件末尾添加多样性检查函数
        content += diversity_check
        
        # 保存修复后的文件
        with open('P.py', 'w', encoding='utf-8') as f:
            f.write(content)
        
        logging.info("✅ P.py关键修复应用完成")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 修复失败: {e}")
        return False

def create_test_script():
    """创建测试脚本验证修复效果"""
    logging.info("📝 创建测试脚本...")
    
    test_script = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 测试P.py修复效果
"""

import logging
import sys
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_fixes():
    """测试修复效果"""
    try:
        # 导入P模块
        import P
        
        # 测试1: 检查特征优化模式
        if hasattr(P, 'ENABLE_FEATURE_OPTIMIZATION'):
            logging.info(f"✅ 特征优化模式: {P.ENABLE_FEATURE_OPTIMIZATION}")
        else:
            logging.error("❌ 缺少ENABLE_FEATURE_OPTIMIZATION")
        
        # 测试2: 检查统一特征集
        if hasattr(P, 'UNIFIED_FEATURE_SET'):
            logging.info(f"✅ 统一特征集: {len(P.UNIFIED_FEATURE_SET)}个特征")
        else:
            logging.error("❌ 缺少UNIFIED_FEATURE_SET")
        
        # 测试3: 检查标准化参数
        if hasattr(P, 'NORMALIZATION_PARAMS'):
            params = P.NORMALIZATION_PARAMS
            if '首板' in params and 'MAIN' in params['首板']:
                logging.info("✅ 首板策略标准化参数存在")
            if '连板' in params and 'MAIN' in params['连板']:
                logging.info("✅ 连板策略标准化参数存在")
        else:
            logging.error("❌ 缺少NORMALIZATION_PARAMS")
        
        # 测试4: 检查关键函数
        functions = ['get_all_feature_columns', 'ensure_feature_consistency_fixed', 'validate_prediction_diversity_fixed']
        for func in functions:
            if hasattr(P, func):
                logging.info(f"✅ 函数存在: {func}")
            else:
                logging.error(f"❌ 函数缺失: {func}")
        
        logging.info("🎉 修复效果测试完成")
        return True
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_fixes()
'''
    
    with open('test_fixes.py', 'w', encoding='utf-8') as f:
        f.write(test_script)
    
    logging.info("✅ 测试脚本创建完成: test_fixes.py")

def main():
    """主函数"""
    print("🚀 P.py关键修复应用程序")
    print("="*80)
    
    # 应用修复
    success = apply_critical_fixes_to_p()
    
    if success:
        print("\n✅ 修复应用成功！")
        print("\n🔧 已应用的修复:")
        print("  1. ✅ 强制启用特征优化模式")
        print("  2. ✅ 修复标准化参数初始化")
        print("  3. ✅ 添加特征一致性检查")
        print("  4. ✅ 添加预测多样性验证")
        
        # 创建测试脚本
        create_test_script()
        
        print("\n🎯 下一步:")
        print("  1. 运行 python3 test_fixes.py 验证修复")
        print("  2. 上传到云服务器测试")
        print("  3. 运行完整的训练预测流程")
        print("  4. 检查AUC和预测多样性")
        
    else:
        print("\n❌ 修复应用失败，请检查错误信息")

if __name__ == "__main__":
    main()
