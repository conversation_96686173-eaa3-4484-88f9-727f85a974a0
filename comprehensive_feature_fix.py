#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔧 综合特征修复方案
基于云服务器日志分析和TBGM文档要求的完整解决方案

问题分析：
1. 特征维度不匹配：训练681/687个特征，预测668个特征
2. 反标准化错误：使用通用参数而非训练时保存的参数
3. 特征集混乱：多个特征定义，动态特征膨胀
4. AUC虚假：特征不一致导致模型性能异常

解决方案：
1. 统一特征集管理
2. 修复反标准化逻辑
3. 确保训练预测特征一致性
4. 逐个测试特征有效性
"""

import logging
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Tuple
import joblib
import os
from sklearn.metrics import roc_auc_score
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class ComprehensiveFeatureFix:
    """综合特征修复类"""
    
    def __init__(self):
        """初始化"""
        self.logger = logging.getLogger(__name__)
        
        # 🎯 基于TBGM文档的核心特征集（21个验证有效特征）
        self.CORE_FEATURES = [
            # 价格特征 (7个)
            'open', 'high', 'low', 'close', 'pre_close', 'pct_chg', 'change',
            
            # 成交量特征 (5个)
            'vol', 'amount', 'turnover_rate', 'turnover_rate_f', 'volume_ratio',
            
            # 估值特征 (5个)
            'pe', 'pe_ttm', 'pb', 'ps', 'ps_ttm',
            
            # 股本特征 (4个)
            'total_share', 'float_share', 'dv_ratio', 'dv_ttm'
        ]
        
        # 📊 扩展特征集（基于日志分析的重要特征）
        self.EXTENDED_FEATURES = [
            # 技术指标
            'rsi2', 'rsi6', 'rsi14', 'macd', 'macd_hist', 'cci', 'wr_14', 'mfi',
            'ma5', 'ma10', 'ma20', 'boll_upper', 'boll_lower', 'atr',
            
            # 资金流向
            '主力净流入占比', '资金技术共振', 'net_mf_amount',
            
            # 市场特征
            '板块联动强度', 'market_pct_std',
            
            # 形态特征
            '真实振幅', '承接力度', '冲高回落指标', '分时走势强度', '竞价异动',
            '振幅', 'GK波动率', '日内高低比'
        ]
        
        # 🎯 最终统一特征集
        self.UNIFIED_FEATURES = self.CORE_FEATURES + self.EXTENDED_FEATURES
        
        self.logger.info(f"🎯 核心特征: {len(self.CORE_FEATURES)}个")
        self.logger.info(f"🎯 扩展特征: {len(self.EXTENDED_FEATURES)}个")
        self.logger.info(f"🎯 统一特征集: {len(self.UNIFIED_FEATURES)}个")
    
    def analyze_feature_dimension_mismatch(self, log_file_path: str) -> Dict[str, Any]:
        """分析特征维度不匹配问题"""
        self.logger.info("🔍 分析特征维度不匹配问题...")
        
        analysis = {
            'training_features': {},
            'prediction_features': 668,
            'missing_features': {},
            'recommendations': []
        }
        
        try:
            with open(log_file_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # 提取训练时特征数量
            import re
            
            # 首板策略特征数量
            shouban_matches = re.findall(r'处理序列数据使用 (\d+) 个有效特征.*首板', content)
            if shouban_matches:
                analysis['training_features']['首板'] = int(shouban_matches[0])
            
            # 连板策略特征数量
            lianban_matches = re.findall(r'处理序列数据使用 (\d+) 个有效特征.*连板', content)
            if lianban_matches:
                analysis['training_features']['连板'] = int(lianban_matches[0])
            
            # 计算缺失特征数量
            for strategy, train_count in analysis['training_features'].items():
                missing = train_count - analysis['prediction_features']
                analysis['missing_features'][strategy] = missing
                
                self.logger.warning(f"⚠️ {strategy}策略: 训练{train_count}个特征，预测{analysis['prediction_features']}个特征，缺失{missing}个")
            
            # 生成建议
            analysis['recommendations'] = [
                "1. 统一训练和预测的特征集",
                "2. 修复特征填充逻辑",
                "3. 确保模型输入维度一致",
                "4. 重新训练模型或调整预测特征"
            ]
            
        except Exception as e:
            self.logger.error(f"❌ 分析失败: {e}")
        
        return analysis
    
    def test_individual_features(self, df: pd.DataFrame, target_col: str = 'future_1_day_limit_up') -> Dict[str, float]:
        """逐个测试特征的AUC表现"""
        self.logger.info("🧪 开始逐个测试特征AUC表现...")
        
        feature_aucs = {}
        
        # 确保目标列存在
        if target_col not in df.columns:
            self.logger.error(f"❌ 目标列 {target_col} 不存在")
            return feature_aucs
        
        y = df[target_col]
        
        for feature in self.UNIFIED_FEATURES:
            if feature in df.columns:
                try:
                    X = df[[feature]].fillna(0)
                    
                    # 检查特征是否有变化
                    if X[feature].nunique() <= 1:
                        feature_aucs[feature] = 0.5  # 常数特征
                        continue
                    
                    # 分割数据
                    X_train, X_test, y_train, y_test = train_test_split(
                        X, y, test_size=0.3, random_state=42, stratify=y
                    )
                    
                    # 训练简单模型
                    model = RandomForestClassifier(n_estimators=10, random_state=42)
                    model.fit(X_train, y_train)
                    
                    # 预测并计算AUC
                    y_pred_proba = model.predict_proba(X_test)[:, 1]
                    auc = roc_auc_score(y_test, y_pred_proba)
                    feature_aucs[feature] = auc
                    
                    self.logger.info(f"  📊 {feature}: AUC={auc:.4f}")
                    
                except Exception as e:
                    self.logger.warning(f"  ⚠️ {feature}: 测试失败 - {e}")
                    feature_aucs[feature] = 0.5
            else:
                self.logger.warning(f"  ❌ {feature}: 特征不存在")
                feature_aucs[feature] = 0.0
        
        # 排序并显示结果
        sorted_features = sorted(feature_aucs.items(), key=lambda x: x[1], reverse=True)
        
        self.logger.info("\n🏆 特征AUC排名（前10）:")
        for i, (feature, auc) in enumerate(sorted_features[:10], 1):
            self.logger.info(f"  {i:2d}. {feature:20s}: {auc:.4f}")
        
        return feature_aucs
    
    def fix_denormalization_logic(self, strategy: str = '首板') -> str:
        """修复反标准化逻辑"""
        self.logger.info(f"🔧 修复{strategy}策略反标准化逻辑...")
        
        fix_code = f'''
def denormalize_predictions_fixed(predictions, strategy_type="{strategy}"):
    """
    🔧 修复版反标准化函数
    使用训练时保存的特定标准化参数，而非通用参数
    """
    import joblib
    import numpy as np
    import logging
    
    try:
        # 🎯 加载训练时保存的标准化参数
        scaler_path = f"models/{{strategy_type}}_scaler.joblib"
        
        if os.path.exists(scaler_path):
            scaler = joblib.load(scaler_path)
            logging.info(f"✅ 成功加载{{strategy_type}}策略标准化器")
            
            # 使用正确的标准化器进行反标准化
            denormalized = scaler.inverse_transform(predictions.reshape(-1, 1)).flatten()
            
            logging.info(f"✅ 使用训练时标准化参数反标准化: {{strategy_type}}")
            return denormalized
            
        else:
            logging.warning(f"⚠️ 标准化器文件不存在: {{scaler_path}}")
            
            # 🔧 使用日志中记录的特定参数
            if strategy_type == "首板":
                # 基于日志: median=1.0000, iqr=19.2948, clip_range=15.0
                median, iqr = 1.0000, 19.2948
            elif strategy_type == "连板":
                # 基于日志: median=1.0000, iqr=15.4950, clip_range=15.0  
                median, iqr = 1.0000, 15.4950
            else:
                median, iqr = 1.0000, 10.0000
            
            # 反标准化公式: denormalized = normalized * iqr + median
            denormalized = predictions * iqr + median
            
            logging.info(f"✅ 使用日志参数反标准化: median={{median}}, iqr={{iqr}}")
            return denormalized
            
    except Exception as e:
        logging.error(f"❌ 反标准化失败: {{e}}")
        return predictions
'''
        
        return fix_code
    
    def create_unified_feature_manager(self) -> str:
        """创建统一特征管理器"""
        self.logger.info("🔧 创建统一特征管理器...")
        
        manager_code = f'''
class UnifiedFeatureManager:
    """统一特征管理器"""
    
    def __init__(self):
        # 🎯 统一特征集（{len(self.UNIFIED_FEATURES)}个特征）
        self.UNIFIED_FEATURES = {self.UNIFIED_FEATURES}
        
        # 📊 特征分类
        self.CORE_FEATURES = {self.CORE_FEATURES}
        self.EXTENDED_FEATURES = {self.EXTENDED_FEATURES}
    
    def get_training_features(self, df):
        """获取训练特征（确保一致性）"""
        available_features = [f for f in self.UNIFIED_FEATURES if f in df.columns]
        missing_features = [f for f in self.UNIFIED_FEATURES if f not in df.columns]
        
        if missing_features:
            logging.warning(f"⚠️ 缺失特征: {{missing_features}}")
        
        logging.info(f"✅ 训练特征: {{len(available_features)}}个")
        return available_features
    
    def get_prediction_features(self, df):
        """获取预测特征（与训练保持一致）"""
        return self.get_training_features(df)
    
    def ensure_feature_consistency(self, train_features, pred_features):
        """确保训练和预测特征一致"""
        if len(train_features) != len(pred_features):
            logging.error(f"❌ 特征数量不一致: 训练{{len(train_features)}}个，预测{{len(pred_features)}}个")
            return False
        
        if set(train_features) != set(pred_features):
            logging.error(f"❌ 特征内容不一致")
            return False
        
        logging.info(f"✅ 特征一致性检查通过: {{len(train_features)}}个特征")
        return True
'''
        
        return manager_code

    def generate_complete_fix_plan(self, log_analysis: Dict[str, Any]) -> Dict[str, Any]:
        """生成完整修复计划"""
        self.logger.info("📋 生成完整修复计划...")

        fix_plan = {
            'immediate_fixes': [
                "1. 修复反标准化函数，使用训练时保存的标准化参数",
                "2. 统一特征集管理，确保训练预测特征一致",
                "3. 修复特征维度不匹配问题",
                "4. 重新验证模型预测多样性"
            ],
            'feature_fixes': [
                f"使用统一特征集: {len(self.UNIFIED_FEATURES)}个特征",
                "移除动态特征生成逻辑",
                "确保所有策略使用相同的特征获取函数",
                "添加特征一致性检查"
            ],
            'model_fixes': [
                "重新训练模型或调整输入维度",
                "验证模型加载和预测流程",
                "确保标准化器正确保存和加载",
                "添加预测结果多样性检查"
            ],
            'validation_steps': [
                "逐个测试特征AUC表现",
                "验证反标准化结果合理性",
                "检查预测结果的分布和多样性",
                "对比修复前后的AUC表现"
            ]
        }

        return fix_plan

def main():
    """主函数"""
    logging.info("🚀 开始综合特征修复...")

    # 初始化修复器
    fixer = ComprehensiveFeatureFix()

    # 分析日志文件
    log_file = "stock_prediction_20250731_144715.log"
    if os.path.exists(log_file):
        analysis = fixer.analyze_feature_dimension_mismatch(log_file)
        logging.info("📊 特征维度分析完成")

        # 生成修复计划
        fix_plan = fixer.generate_complete_fix_plan(analysis)

        print("\n" + "="*80)
        print("🎯 综合特征修复方案")
        print("="*80)

        print("\n📊 问题分析:")
        for strategy, count in analysis['training_features'].items():
            missing = analysis['missing_features'][strategy]
            print(f"  - {strategy}策略: 训练{count}个特征，预测668个特征，缺失{missing}个")

        print("\n🔧 立即修复:")
        for fix in fix_plan['immediate_fixes']:
            print(f"  {fix}")

        print("\n📋 详细修复计划:")
        for category, fixes in fix_plan.items():
            if category != 'immediate_fixes':
                print(f"\n  {category.replace('_', ' ').title()}:")
                for fix in fixes:
                    print(f"    - {fix}")

        # 生成修复代码
        print("\n🔧 反标准化修复代码已生成")
        print("🔧 统一特征管理器已生成")

    else:
        logging.error(f"❌ 日志文件不存在: {log_file}")

if __name__ == "__main__":
    main()
