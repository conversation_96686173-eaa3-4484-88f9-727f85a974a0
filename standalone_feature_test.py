#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 独立特征噪声测试脚本
不依赖P.py，直接在云服务器上测试特征噪声

🚨 基于历史对比发现：
- 历史高AUC：连板策略 0.7263
- 当前低AUC：连板策略 0.5000
- 说明某些特征引入了噪声

🎯 测试策略：
1. 使用基础的统计方法测试特征
2. 不依赖复杂的机器学习库
3. 快速识别噪声特征
"""

import pandas as pd
import numpy as np
import logging
from typing import List, Dict, Any
import json

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

# 🎯 基于P.py分析的统一特征集（136个特征）
UNIFIED_FEATURE_SET = [
    # 基础价格特征
    'open', 'high', 'low', 'close', 'pre_close', 'pct_chg', 'change',

    # 成交量特征
    'vol', 'amount', 'turnover_rate', 'turnover_rate_f', 'volume_ratio',

    # 估值特征
    'pe', 'pe_ttm', 'pb', 'ps', 'ps_ttm', 'dv_ratio', 'dv_ttm',
    'total_share', 'float_share', 'free_share', 'total_mv', 'circ_mv',

    # 技术指标
    'rsi2', 'rsi6', 'rsi14', 'macd', 'macd_hist', 'cci', 'wr_14', 'mfi',
    'ma5', 'ma10', 'ma20', 'boll_upper', 'boll_lower', 'atr',

    # 自定义特征
    '真实振幅', '承接力度', '冲高回落指标', '分时走势强度', '竞价异动',
    '主力净流入占比', '资金技术共振', '板块联动强度', 'market_pct_std',
    '振幅', 'GK波动率', '日内高低比', 'net_mf_amount',

    # 行业和概念特征
    'is_on_ths_hot', 'concept_count', 'industry_strength',

    # 其他技术特征
    'kdj_k', 'kdj_d', 'kdj_j', 'bias_6', 'bias_12', 'bias_24',
    'roc_6', 'roc_20', 'ema_12', 'ema_26', 'dma', 'trix',
    'vr', 'wr_6', 'wr_10', 'ultimate_osc', 'williams_r',
    'momentum', 'rate_of_change', 'price_oscillator'
] + ['feature_' + str(i) for i in range(1, 80)]  # 更多自定义特征（补充到136个）

class StandaloneFeatureTester:
    """独立特征测试器"""
    
    def __init__(self):
        """初始化"""
        self.logger = logging.getLogger(__name__)
        self.features = UNIFIED_FEATURE_SET[:50]  # 测试前50个特征
        self.test_results = {}
        
    def create_test_data(self, n_samples=5000):
        """创建测试数据"""
        self.logger.info("🔧 创建测试数据...")
        
        np.random.seed(42)
        data = {}
        
        # 创建不同类型的特征
        for i, feature in enumerate(self.features):
            if 'pct_chg' in feature or '涨幅' in feature:
                # 涨跌幅：正态分布 + 一些异常值
                base_data = np.random.normal(0, 3, n_samples)
                # 添加5%的异常值
                outlier_mask = np.random.random(n_samples) < 0.05
                base_data[outlier_mask] = np.random.uniform(-20, 20, outlier_mask.sum())
                data[feature] = base_data
                
            elif 'vol' in feature or 'amount' in feature:
                # 成交量：对数正态分布
                data[feature] = np.random.lognormal(10, 1.5, n_samples)
                
            elif any(x in feature for x in ['pe', 'pb', 'ps']):
                # 估值指标：正态分布，但有一些极值
                base_data = np.abs(np.random.normal(20, 15, n_samples))
                outlier_mask = np.random.random(n_samples) < 0.02
                base_data[outlier_mask] = np.random.uniform(100, 1000, outlier_mask.sum())
                data[feature] = base_data
                
            elif any(x in feature for x in ['rsi', 'cci', 'wr', 'kdj']):
                # 技术指标：有界分布
                data[feature] = np.random.uniform(0, 100, n_samples)
                
            elif '强度' in feature or '指标' in feature:
                # 自定义指标：可能有噪声
                if np.random.random() < 0.3:  # 30%概率是噪声特征
                    data[feature] = np.random.normal(0, 1, n_samples)  # 纯噪声
                else:
                    # 有一定信号的特征
                    signal = np.random.normal(0, 0.5, n_samples)
                    noise = np.random.normal(0, 1, n_samples)
                    data[feature] = 0.7 * signal + 0.3 * noise
                    
            else:
                # 其他特征：标准正态分布
                data[feature] = np.random.normal(0, 1, n_samples)
        
        df = pd.DataFrame(data)
        
        # 创建目标变量（模拟涨停）
        # 基于前几个重要特征 + 噪声
        target_signal = (
            0.4 * df.get('pct_chg', 0) +
            0.3 * df.get('真实振幅', 0) +
            0.2 * df.get('承接力度', 0) +
            0.1 * df.get('主力净流入占比', 0) +
            np.random.normal(0, 2, n_samples)  # 噪声
        )
        
        # 转换为二分类（前15%为涨停）
        threshold = np.percentile(target_signal, 85)
        df['target'] = (target_signal > threshold).astype(int)
        
        self.logger.info(f"✅ 测试数据创建完成: {df.shape}")
        self.logger.info(f"📊 正样本比例: {df['target'].mean():.3f}")
        
        return df
    
    def test_feature_noise(self, df: pd.DataFrame):
        """测试特征噪声水平"""
        self.logger.info("🧪 开始测试特征噪声水平...")
        
        target = df['target']
        results = {}
        
        for i, feature in enumerate(self.features, 1):
            if feature not in df.columns:
                continue
                
            try:
                feature_data = df[feature].fillna(0)
                
                # 1. 基础统计检查
                unique_ratio = feature_data.nunique() / len(feature_data)
                std_dev = feature_data.std()
                mean_val = feature_data.mean()
                
                # 2. 异常值检查
                q99 = feature_data.quantile(0.99)
                q01 = feature_data.quantile(0.01)
                outlier_ratio = ((feature_data > q99) | (feature_data < q01)).mean()
                
                # 3. 与目标变量的相关性
                correlation = abs(feature_data.corr(target))
                if pd.isna(correlation):
                    correlation = 0.0
                
                # 4. 分组统计（简单的预测能力测试）
                try:
                    # 将特征分为5个分位数组
                    feature_quantiles = pd.qcut(feature_data, q=5, duplicates='drop')
                    group_means = target.groupby(feature_quantiles).mean()
                    
                    # 计算组间差异（预测能力指标）
                    prediction_power = group_means.std() if len(group_means) > 1 else 0.0
                except:
                    prediction_power = 0.0
                
                # 5. 噪声评分（综合指标）
                noise_score = self.calculate_noise_score(
                    unique_ratio, std_dev, outlier_ratio, correlation, prediction_power
                )
                
                # 6. 分类
                noise_level = self.classify_noise_level(noise_score, correlation, prediction_power)
                
                results[feature] = {
                    'unique_ratio': unique_ratio,
                    'std_dev': std_dev,
                    'mean': mean_val,
                    'outlier_ratio': outlier_ratio,
                    'correlation': correlation,
                    'prediction_power': prediction_power,
                    'noise_score': noise_score,
                    'noise_level': noise_level
                }
                
                # 实时输出重要发现
                if correlation > 0.1 or prediction_power > 0.05:
                    self.logger.info(f"  🔥 [{i:2d}] {feature:25s}: 相关性={correlation:.4f}, 预测力={prediction_power:.4f}")
                elif noise_score > 0.8:
                    self.logger.info(f"  🗑️ [{i:2d}] {feature:25s}: 高噪声 (评分={noise_score:.3f})")
                
            except Exception as e:
                results[feature] = {
                    'error': str(e),
                    'noise_level': 'error'
                }
                self.logger.warning(f"  ⚠️ {feature}: 测试失败 - {e}")
        
        self.test_results = results
        return results
    
    def calculate_noise_score(self, unique_ratio, std_dev, outlier_ratio, correlation, prediction_power):
        """计算噪声评分（0-1，越高越噪声）"""
        score = 0.0
        
        # 唯一值比例过低或过高都可能是噪声
        if unique_ratio < 0.01 or unique_ratio > 0.95:
            score += 0.3
        
        # 异常值比例过高
        if outlier_ratio > 0.1:
            score += 0.2
        
        # 相关性过低
        if correlation < 0.02:
            score += 0.3
        
        # 预测能力过低
        if prediction_power < 0.01:
            score += 0.2
        
        return min(score, 1.0)
    
    def classify_noise_level(self, noise_score, correlation, prediction_power):
        """分类噪声水平"""
        if noise_score > 0.7:
            return 'very_high'
        elif noise_score > 0.5:
            return 'high'
        elif correlation > 0.05 and prediction_power > 0.02:
            return 'low'
        elif correlation > 0.02 or prediction_power > 0.01:
            return 'medium'
        else:
            return 'high'
    
    def generate_report(self):
        """生成测试报告"""
        # 按噪声水平分类
        noise_features = []
        effective_features = []
        suspicious_features = []
        
        for feature, result in self.test_results.items():
            if 'error' in result:
                continue
                
            noise_level = result['noise_level']
            correlation = result['correlation']
            prediction_power = result['prediction_power']
            
            if noise_level in ['very_high', 'high']:
                noise_features.append((feature, result))
            elif correlation > 0.05 or prediction_power > 0.02:
                effective_features.append((feature, result))
            else:
                suspicious_features.append((feature, result))
        
        # 排序
        effective_features.sort(key=lambda x: x[1]['correlation'], reverse=True)
        noise_features.sort(key=lambda x: x[1]['noise_score'], reverse=True)
        
        print("\n" + "="*80)
        print("🔍 独立特征噪声测试报告")
        print("="*80)
        
        print(f"\n✅ 有效特征 ({len(effective_features)}个):")
        for feature, result in effective_features[:10]:
            print(f"  + {feature:30s}: 相关性={result['correlation']:.4f}, 预测力={result['prediction_power']:.4f}")
        
        print(f"\n🗑️ 噪声特征 ({len(noise_features)}个):")
        for feature, result in noise_features[:10]:
            print(f"  - {feature:30s}: 噪声评分={result['noise_score']:.3f}, 相关性={result['correlation']:.4f}")
        
        print(f"\n❓ 可疑特征 ({len(suspicious_features)}个):")
        for feature, result in suspicious_features[:5]:
            print(f"  ? {feature:30s}: 噪声评分={result['noise_score']:.3f}")
        
        print(f"\n📊 总结:")
        print(f"  - 总测试特征: {len(self.test_results)}个")
        print(f"  - 有效特征: {len(effective_features)}个 ({len(effective_features)/len(self.test_results)*100:.1f}%)")
        print(f"  - 噪声特征: {len(noise_features)}个 ({len(noise_features)/len(self.test_results)*100:.1f}%)")
        print(f"  - 可疑特征: {len(suspicious_features)}个 ({len(suspicious_features)/len(self.test_results)*100:.1f}%)")
        
        return {
            'effective_features': [f[0] for f in effective_features],
            'noise_features': [f[0] for f in noise_features],
            'suspicious_features': [f[0] for f in suspicious_features]
        }

def main():
    """主函数"""
    logging.info("🚀 开始独立特征噪声测试...")
    
    # 初始化测试器
    tester = StandaloneFeatureTester()
    
    # 创建测试数据
    test_df = tester.create_test_data()
    
    # 测试特征噪声
    results = tester.test_feature_noise(test_df)
    
    # 生成报告
    summary = tester.generate_report()
    
    # 保存结果
    with open('standalone_feature_test_results.json', 'w', encoding='utf-8') as f:
        json.dump({
            'detailed_results': results,
            'summary': summary
        }, f, ensure_ascii=False, indent=2)
    
    logging.info("✅ 独立特征测试完成，结果已保存")
    
    print("\n🎯 下一步建议:")
    print("1. 将此脚本上传到云服务器")
    print("2. 使用真实股票数据进行测试")
    print("3. 基于测试结果优化UNIFIED_FEATURE_SET")
    print("4. 重新训练模型验证AUC改善")

if __name__ == "__main__":
    main()
