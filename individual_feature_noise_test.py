#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 单个特征噪声测试脚本
基于历史高AUC日志对比分析，逐个测试每个特征的噪声水平

🚨 关键发现：
- 历史日志：连板策略AUC = 0.7263 (很好)
- 当前日志：连板策略AUC = 0.5000 (随机)
- 说明某些特征引入了噪声，导致性能下降

🎯 测试目标：
1. 逐个测试每个特征的AUC表现
2. 识别噪声特征（AUC接近0.5或异常值）
3. 识别有效特征（AUC明显偏离0.5）
4. 构建清洁特征集
"""

import logging
import pandas as pd
import numpy as np
from typing import List, Dict, Any, Tuple
from sklearn.metrics import roc_auc_score
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
from sklearn.preprocessing import StandardScaler
import warnings
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

class IndividualFeatureNoiseTester:
    """单个特征噪声测试器"""
    
    def __init__(self):
        """初始化"""
        self.logger = logging.getLogger(__name__)
        
        # 🎯 当前使用的统一特征集（从P.py获取）
        self.current_features = None
        
        # 📊 测试结果存储
        self.feature_test_results = {}
        self.noise_features = []
        self.effective_features = []
        self.suspicious_features = []
        
    def load_current_features(self):
        """加载当前P.py中使用的特征集"""
        try:
            import P
            if hasattr(P, 'UNIFIED_FEATURE_SET'):
                self.current_features = P.UNIFIED_FEATURE_SET.copy()
                self.logger.info(f"✅ 加载当前特征集: {len(self.current_features)}个特征")
            else:
                self.logger.error("❌ P.py中没有UNIFIED_FEATURE_SET")
                return False
            return True
        except Exception as e:
            self.logger.error(f"❌ 加载特征集失败: {e}")
            return False
    
    def create_synthetic_test_data(self, n_samples=10000):
        """创建合成测试数据"""
        self.logger.info("🔧 创建合成测试数据...")
        
        np.random.seed(42)  # 确保可重复性
        
        # 创建特征数据
        data = {}
        
        for i, feature in enumerate(self.current_features):
            if 'pct_chg' in feature or '涨幅' in feature:
                # 涨跌幅特征：正态分布，均值0，标准差5%
                data[feature] = np.random.normal(0, 5, n_samples)
            elif 'vol' in feature or '成交' in feature or 'amount' in feature:
                # 成交量特征：对数正态分布
                data[feature] = np.random.lognormal(10, 1, n_samples)
            elif 'pe' in feature or 'pb' in feature or 'ps' in feature:
                # 估值特征：正态分布，均值20，标准差10
                data[feature] = np.abs(np.random.normal(20, 10, n_samples))
            elif 'rsi' in feature or 'cci' in feature or 'wr' in feature:
                # 技术指标：0-100范围
                data[feature] = np.random.uniform(0, 100, n_samples)
            elif '强度' in feature or '指标' in feature or '比例' in feature:
                # 自定义指标：正态分布
                data[feature] = np.random.normal(0, 1, n_samples)
            else:
                # 其他特征：标准正态分布
                data[feature] = np.random.normal(0, 1, n_samples)
        
        df = pd.DataFrame(data)
        
        # 创建目标变量（涨停）
        # 基于一些特征的线性组合 + 噪声
        target_signal = (
            0.3 * df.get('pct_chg', 0) +
            0.2 * df.get('真实振幅', 0) +
            0.1 * df.get('承接力度', 0) +
            np.random.normal(0, 1, n_samples)
        )
        
        # 转换为二分类标签（前20%为正样本）
        threshold = np.percentile(target_signal, 80)
        df['future_1_day_limit_up'] = (target_signal > threshold).astype(int)
        
        self.logger.info(f"✅ 合成数据创建完成: {df.shape}")
        self.logger.info(f"📊 正样本比例: {df['future_1_day_limit_up'].mean():.3f}")
        
        return df
    
    def test_individual_feature_auc(self, df: pd.DataFrame, target_col: str = 'future_1_day_limit_up') -> Dict[str, Dict]:
        """逐个测试每个特征的AUC表现"""
        self.logger.info("🧪 开始逐个测试特征AUC表现...")
        
        results = {}
        y = df[target_col]
        
        for i, feature in enumerate(self.current_features, 1):
            if feature not in df.columns:
                results[feature] = {
                    'auc': 0.0,
                    'status': 'missing',
                    'noise_level': 'unknown',
                    'recommendation': 'remove'
                }
                continue
            
            try:
                X = df[[feature]].fillna(0)
                
                # 检查特征变化
                if X[feature].nunique() <= 1:
                    results[feature] = {
                        'auc': 0.5,
                        'status': 'constant',
                        'noise_level': 'high',
                        'recommendation': 'remove'
                    }
                    continue
                
                # 检查异常值
                q99 = X[feature].quantile(0.99)
                q01 = X[feature].quantile(0.01)
                outlier_ratio = ((X[feature] > q99) | (X[feature] < q01)).mean()
                
                # 分割数据
                X_train, X_test, y_train, y_test = train_test_split(
                    X, y, test_size=0.3, random_state=42, stratify=y
                )
                
                # 标准化
                scaler = StandardScaler()
                X_train_scaled = scaler.fit_transform(X_train)
                X_test_scaled = scaler.transform(X_test)
                
                # 训练简单模型
                model = RandomForestClassifier(n_estimators=50, random_state=42, max_depth=5)
                model.fit(X_train_scaled, y_train)
                
                # 预测并计算AUC
                y_pred_proba = model.predict_proba(X_test_scaled)[:, 1]
                auc = roc_auc_score(y_test, y_pred_proba)
                
                # 分析噪声水平
                noise_level = self.analyze_noise_level(auc, outlier_ratio, X[feature])
                
                # 生成建议
                recommendation = self.generate_recommendation(auc, noise_level, outlier_ratio)
                
                results[feature] = {
                    'auc': auc,
                    'status': 'tested',
                    'noise_level': noise_level,
                    'outlier_ratio': outlier_ratio,
                    'recommendation': recommendation,
                    'std': X[feature].std(),
                    'mean': X[feature].mean()
                }
                
                # 实时输出进度
                if i % 10 == 0 or auc > 0.6 or auc < 0.4:
                    status_emoji = "🔥" if auc > 0.6 else "❄️" if auc < 0.4 else "📊"
                    self.logger.info(f"  {status_emoji} [{i:3d}/{len(self.current_features)}] {feature:25s}: AUC={auc:.4f} ({noise_level})")
                
            except Exception as e:
                results[feature] = {
                    'auc': 0.5,
                    'status': 'error',
                    'noise_level': 'unknown',
                    'recommendation': 'remove',
                    'error': str(e)
                }
                self.logger.warning(f"  ⚠️ {feature}: 测试失败 - {e}")
        
        self.feature_test_results = results
        return results
    
    def analyze_noise_level(self, auc: float, outlier_ratio: float, feature_series: pd.Series) -> str:
        """分析特征噪声水平"""
        # AUC接近0.5表示噪声高
        auc_deviation = abs(auc - 0.5)
        
        # 异常值比例高表示噪声高
        # 标准差过大或过小也可能表示噪声
        std_ratio = feature_series.std() / (abs(feature_series.mean()) + 1e-8)
        
        if auc_deviation < 0.02:  # AUC在0.48-0.52之间
            return 'very_high'
        elif auc_deviation < 0.05:  # AUC在0.45-0.55之间
            return 'high'
        elif auc_deviation < 0.1:  # AUC在0.4-0.6之间
            return 'medium'
        elif auc_deviation < 0.2:  # AUC在0.3-0.7之间
            return 'low'
        else:
            return 'very_low'
    
    def generate_recommendation(self, auc: float, noise_level: str, outlier_ratio: float) -> str:
        """生成特征使用建议"""
        if noise_level in ['very_high', 'high']:
            return 'remove'
        elif auc > 0.6 or auc < 0.4:
            return 'keep'
        elif outlier_ratio > 0.1:
            return 'clean'
        else:
            return 'test_further'
    
    def categorize_features(self):
        """对特征进行分类"""
        self.noise_features = []
        self.effective_features = []
        self.suspicious_features = []
        
        for feature, result in self.feature_test_results.items():
            auc = result['auc']
            noise_level = result['noise_level']
            recommendation = result['recommendation']
            
            if recommendation == 'remove' or noise_level in ['very_high', 'high']:
                self.noise_features.append(feature)
            elif recommendation == 'keep' or (auc > 0.6 or auc < 0.4):
                self.effective_features.append(feature)
            else:
                self.suspicious_features.append(feature)
        
        self.logger.info(f"📊 特征分类完成:")
        self.logger.info(f"  🗑️ 噪声特征: {len(self.noise_features)}个")
        self.logger.info(f"  ✅ 有效特征: {len(self.effective_features)}个")
        self.logger.info(f"  ❓ 可疑特征: {len(self.suspicious_features)}个")
    
    def generate_report(self) -> str:
        """生成详细测试报告"""
        report = []
        report.append("🔍 单个特征噪声测试报告")
        report.append("=" * 80)
        
        # 按AUC排序
        sorted_results = sorted(
            self.feature_test_results.items(),
            key=lambda x: abs(x[1]['auc'] - 0.5),
            reverse=True
        )
        
        report.append(f"\n📊 特征AUC排名（按偏离随机水平程度排序）:")
        report.append("-" * 80)
        
        for i, (feature, result) in enumerate(sorted_results[:20], 1):
            auc = result['auc']
            noise_level = result['noise_level']
            recommendation = result['recommendation']
            
            status_emoji = "🔥" if auc > 0.6 else "❄️" if auc < 0.4 else "📊"
            rec_emoji = "✅" if recommendation == 'keep' else "🗑️" if recommendation == 'remove' else "❓"
            
            report.append(f"{i:2d}. {status_emoji}{rec_emoji} {feature:30s}: AUC={auc:.4f} ({noise_level})")
        
        report.append(f"\n🗑️ 建议移除的噪声特征 ({len(self.noise_features)}个):")
        for feature in self.noise_features[:10]:
            auc = self.feature_test_results[feature]['auc']
            report.append(f"  - {feature:30s}: AUC={auc:.4f}")
        
        report.append(f"\n✅ 建议保留的有效特征 ({len(self.effective_features)}个):")
        for feature in self.effective_features[:10]:
            auc = self.feature_test_results[feature]['auc']
            report.append(f"  + {feature:30s}: AUC={auc:.4f}")
        
        return "\n".join(report)

def main():
    """主函数"""
    logging.info("🚀 开始单个特征噪声测试...")
    
    # 初始化测试器
    tester = IndividualFeatureNoiseTester()
    
    # 加载当前特征集
    if not tester.load_current_features():
        logging.error("❌ 无法加载特征集，退出测试")
        return
    
    # 创建测试数据
    test_df = tester.create_synthetic_test_data()
    
    # 逐个测试特征
    results = tester.test_individual_feature_auc(test_df)
    
    # 分类特征
    tester.categorize_features()
    
    # 生成报告
    report = tester.generate_report()
    print(report)
    
    # 保存结果
    import json
    with open('feature_noise_test_results.json', 'w', encoding='utf-8') as f:
        json.dump(results, f, ensure_ascii=False, indent=2)
    
    logging.info("✅ 特征噪声测试完成，结果已保存到 feature_noise_test_results.json")

if __name__ == "__main__":
    main()
