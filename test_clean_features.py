#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 测试清洁特征集的效果
验证移除数据泄漏特征后的AUC表现
"""

import logging
import pandas as pd
import numpy as np
from sklearn.metrics import roc_auc_score
from sklearn.model_selection import train_test_split
from sklearn.ensemble import RandomForestClassifier
import warnings
warnings.filterwarnings('ignore')

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def create_synthetic_data_with_leakage():
    """创建包含数据泄漏的合成数据"""
    np.random.seed(42)
    n_samples = 10000
    
    # 创建基础特征
    data = {
        'pct_chg': np.random.normal(0, 3, n_samples),
        'volume_ratio': np.random.lognormal(0, 0.5, n_samples),
        'turnover_rate': np.random.uniform(0, 20, n_samples),
        'rsi': np.random.uniform(0, 100, n_samples),
        'macd': np.random.normal(0, 0.1, n_samples),
    }
    
    # 创建目标变量（涨停）
    # 基于真实的金融逻辑：大涨幅、高成交量比例更容易涨停
    limit_up_prob = (
        (data['pct_chg'] > 5).astype(float) * 0.3 +  # 大涨幅
        (data['volume_ratio'] > 2).astype(float) * 0.2 +  # 高成交量
        (data['turnover_rate'] > 10).astype(float) * 0.1 +  # 高换手率
        np.random.uniform(0, 0.1, n_samples)  # 随机因素
    )
    
    target = (np.random.uniform(0, 1, n_samples) < limit_up_prob).astype(int)
    
    # 🚨 添加数据泄漏特征（直接基于目标变量）
    data['涨停强度'] = target * np.random.uniform(0.8, 1.0, n_samples) + \
                    (1 - target) * np.random.uniform(0, 0.3, n_samples)
    
    data['连续涨停天数'] = target * np.random.poisson(2, n_samples) + \
                      (1 - target) * np.random.poisson(0.1, n_samples)
    
    data['涨停概率'] = target * np.random.uniform(0.7, 1.0, n_samples) + \
                    (1 - target) * np.random.uniform(0, 0.4, n_samples)
    
    # 添加目标变量
    data['target'] = target
    
    df = pd.DataFrame(data)
    
    logging.info(f"✅ 创建合成数据: {len(df)}行, {len(df.columns)-1}个特征")
    logging.info(f"📊 涨停比例: {target.mean():.2%}")
    
    return df

def test_feature_sets(df):
    """测试不同特征集的AUC表现"""
    
    # 定义特征集
    feature_sets = {
        'clean_features': ['pct_chg', 'volume_ratio', 'turnover_rate', 'rsi', 'macd'],
        'with_leakage': ['pct_chg', 'volume_ratio', 'turnover_rate', 'rsi', 'macd', 
                        '涨停强度', '连续涨停天数', '涨停概率'],
        'only_leakage': ['涨停强度', '连续涨停天数', '涨停概率']
    }
    
    results = {}
    
    for set_name, features in feature_sets.items():
        logging.info(f"\n🧪 测试特征集: {set_name}")
        logging.info(f"📊 特征数量: {len(features)}")
        
        # 准备数据
        X = df[features]
        y = df['target']
        
        # 分割数据
        X_train, X_test, y_train, y_test = train_test_split(
            X, y, test_size=0.3, random_state=42, stratify=y
        )
        
        # 训练模型
        model = RandomForestClassifier(n_estimators=100, random_state=42)
        model.fit(X_train, y_train)
        
        # 预测
        y_pred_proba = model.predict_proba(X_test)[:, 1]
        
        # 计算AUC
        auc = roc_auc_score(y_test, y_pred_proba)
        
        results[set_name] = {
            'auc': auc,
            'features': features,
            'feature_count': len(features)
        }
        
        logging.info(f"✅ AUC: {auc:.4f}")
        
        # 特征重要性
        importance = model.feature_importances_
        feature_importance = list(zip(features, importance))
        feature_importance.sort(key=lambda x: x[1], reverse=True)
        
        logging.info("📊 特征重要性 (Top 5):")
        for i, (feat, imp) in enumerate(feature_importance[:5], 1):
            logging.info(f"  {i}. {feat:20s}: {imp:.4f}")
    
    return results

def analyze_results(results):
    """分析测试结果"""
    logging.info("\n" + "="*80)
    logging.info("📊 特征集AUC对比分析")
    logging.info("="*80)
    
    for set_name, result in results.items():
        auc = result['auc']
        count = result['feature_count']
        
        if auc > 0.9:
            status = "🚨 可能存在数据泄漏"
        elif auc > 0.8:
            status = "✅ 优秀表现"
        elif auc > 0.7:
            status = "✅ 良好表现"
        elif auc > 0.6:
            status = "⚠️ 一般表现"
        else:
            status = "❌ 表现较差"
        
        logging.info(f"{set_name:15s}: AUC={auc:.4f} ({count:2d}个特征) - {status}")
    
    # 计算改进效果
    if 'clean_features' in results and 'with_leakage' in results:
        clean_auc = results['clean_features']['auc']
        leakage_auc = results['with_leakage']['auc']
        
        if leakage_auc > clean_auc:
            logging.info(f"\n🔍 数据泄漏检测:")
            logging.info(f"  - 包含泄漏特征AUC: {leakage_auc:.4f}")
            logging.info(f"  - 清洁特征AUC: {clean_auc:.4f}")
            logging.info(f"  - 差异: {leakage_auc - clean_auc:.4f}")
            
            if leakage_auc - clean_auc > 0.1:
                logging.warning("⚠️ 检测到显著的数据泄漏！")
            else:
                logging.info("✅ 数据泄漏影响较小")

def main():
    """主函数"""
    logging.info("🚀 开始清洁特征集测试")
    
    # 1. 创建测试数据
    df = create_synthetic_data_with_leakage()
    
    # 2. 测试不同特征集
    results = test_feature_sets(df)
    
    # 3. 分析结果
    analyze_results(results)
    
    # 4. 结论
    logging.info("\n" + "="*80)
    logging.info("🎯 测试结论")
    logging.info("="*80)
    logging.info("1. 数据泄漏特征会显著提高AUC，但这是虚假的性能提升")
    logging.info("2. 清洁特征集虽然AUC较低，但反映了真实的预测能力")
    logging.info("3. 移除数据泄漏特征是获得可靠模型的关键步骤")
    logging.info("4. P.py中的AUC=0.5问题很可能就是由数据泄漏特征导致的")

if __name__ == "__main__":
    main()
