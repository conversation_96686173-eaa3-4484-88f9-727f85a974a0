#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 测试最终优化效果
"""

import logging
import sys

def test_final_optimization():
    """测试最终优化效果"""
    logging.info("🧪 开始测试最终优化效果...")
    
    try:
        # 导入优化后的P模块
        import P
        
        # 测试特征获取
        import pandas as pd
        test_data = {f: [1, 2, 3] for f in ['pct_chg', 'change', 'open', 'close']}
        test_df = pd.DataFrame(test_data)
        
        features = P.get_all_feature_columns(test_df)
        
        logging.info(f"✅ 特征获取测试通过")
        logging.info(f"📊 获取特征数量: {len(features)}")
        logging.info(f"📊 预期特征数量: 17")
        logging.info(f"📊 测试结果: {'✅ 通过' if len(features) <= 20 else '❌ 失败'}")
        
        # 检查关键特征
        key_features = ['pct_chg', 'change', '主力净流入占比']
        missing_key = [f for f in key_features if f not in features]
        
        if missing_key:
            logging.warning(f"⚠️ 缺失关键特征: {missing_key}")
        else:
            logging.info("✅ 关键特征检查通过")
        
        return True
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    logging.basicConfig(level=logging.INFO)
    test_final_optimization()
