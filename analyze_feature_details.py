#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🔍 详细特征分析 - 回答683个特征的问题
分析P.py中的所有特征定义和实际使用情况
"""

import logging
import re
import pandas as pd

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def analyze_feature_definitions():
    """分析P.py中的特征定义"""
    print("🔍 分析P.py中的特征定义...")
    
    try:
        with open('P.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 1. 分析UNIFIED_FEATURE_SET
        print("\n" + "="*80)
        print("📊 1. UNIFIED_FEATURE_SET 分析")
        print("="*80)
        
        unified_match = re.search(r'UNIFIED_FEATURE_SET\s*=\s*\[(.*?)\]', content, re.DOTALL)
        if unified_match:
            unified_content = unified_match.group(1)
            # 提取特征名称
            features = re.findall(r"'([^']+)'", unified_content)
            print(f"📊 UNIFIED_FEATURE_SET 特征数量: {len(features)}")
            print(f"📊 特征列表:")
            for i, feature in enumerate(features, 1):
                print(f"  {i:2d}. {feature}")
        else:
            print("❌ 未找到UNIFIED_FEATURE_SET定义")
        
        # 2. 分析get_all_feature_columns_final_fix函数
        print("\n" + "="*80)
        print("📊 2. get_all_feature_columns_final_fix 函数分析")
        print("="*80)
        
        func_matches = re.findall(r'def get_all_feature_columns_final_fix.*?CORE_FEATURES\s*=\s*\[(.*?)\]', content, re.DOTALL)
        for i, match in enumerate(func_matches, 1):
            features = re.findall(r"'([^']+)'", match)
            print(f"📊 函数定义 {i} - 特征数量: {len(features)}")
            print(f"📊 特征列表:")
            for j, feature in enumerate(features, 1):
                print(f"  {j:2d}. {feature}")
            print()
        
        # 3. 分析其他特征集定义
        print("\n" + "="*80)
        print("📊 3. 其他特征集定义分析")
        print("="*80)
        
        other_feature_sets = [
            'FEATURE_COLUMNS',
            'EFFECTIVE_FEATURES', 
            'CLEAN_FEATURES',
            'FINAL_CORE_FEATURES'
        ]
        
        for feature_set in other_feature_sets:
            pattern = rf'{feature_set}\s*=\s*([^\n]+)'
            matches = re.findall(pattern, content)
            if matches:
                print(f"📊 {feature_set}:")
                for match in matches:
                    if 'UNIFIED_FEATURE_SET' in match:
                        print(f"  -> 引用 UNIFIED_FEATURE_SET")
                    else:
                        # 尝试提取特征列表
                        if '[' in match and ']' in match:
                            features = re.findall(r"'([^']+)'", match)
                            print(f"  -> {len(features)} 个特征: {features[:5]}{'...' if len(features) > 5 else ''}")
                        else:
                            print(f"  -> {match}")
            else:
                print(f"📊 {feature_set}: 未找到定义")
        
        # 4. 分析实际训练中使用的特征数量
        print("\n" + "="*80)
        print("📊 4. 实际训练特征数量分析")
        print("="*80)
        
        # 查找日志中的特征数量信息
        feature_count_patterns = [
            r'使用.*?(\d+).*?个特征',
            r'特征数量.*?(\d+)',
            r'总特征数.*?(\d+)',
            r'feature.*?count.*?(\d+)',
        ]
        
        for pattern in feature_count_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            if matches:
                print(f"📊 找到特征数量引用: {matches}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def test_actual_feature_usage():
    """测试实际特征使用情况"""
    print("\n" + "="*80)
    print("🧪 5. 实际特征使用测试")
    print("="*80)
    
    try:
        import P
        import pandas as pd
        
        # 创建测试数据
        test_data = {
            'pct_chg': [1.0, 2.0, 3.0],
            'change': [0.1, 0.2, 0.3],
            'open': [10.0, 11.0, 12.0],
            'high': [10.5, 11.5, 12.5],
            'low': [9.5, 10.5, 11.5],
            'close': [10.2, 11.2, 12.2],
            'vol': [1000, 1100, 1200],
            'amount': [10000, 11000, 12000],
            # 添加一些额外特征测试
            'extra_feature_1': [0.1, 0.2, 0.3],
            'extra_feature_2': [0.4, 0.5, 0.6],
        }
        
        test_df = pd.DataFrame(test_data)
        print(f"📊 测试数据创建: {test_df.shape[1]} 列")
        
        # 测试get_all_feature_columns_final_fix函数
        if hasattr(P, 'get_all_feature_columns_final_fix'):
            features = P.get_all_feature_columns_final_fix(test_df)
            print(f"📊 get_all_feature_columns_final_fix 返回: {len(features)} 个特征")
            print(f"📊 特征列表: {features}")
        else:
            print("❌ get_all_feature_columns_final_fix 函数不存在")
        
        # 测试UNIFIED_FEATURE_SET
        if hasattr(P, 'UNIFIED_FEATURE_SET'):
            print(f"📊 P.UNIFIED_FEATURE_SET: {len(P.UNIFIED_FEATURE_SET)} 个特征")
            print(f"📊 前10个特征: {P.UNIFIED_FEATURE_SET[:10]}")
        else:
            print("❌ UNIFIED_FEATURE_SET 不存在")
        
        # 测试FEATURE_COLUMNS
        if hasattr(P, 'FEATURE_COLUMNS'):
            print(f"📊 P.FEATURE_COLUMNS: {len(P.FEATURE_COLUMNS)} 个特征")
            print(f"📊 前10个特征: {P.FEATURE_COLUMNS[:10]}")
        else:
            print("❌ FEATURE_COLUMNS 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ 测试失败: {e}")
        import traceback
        traceback.print_exc()
        return False

def analyze_683_feature_mystery():
    """分析683个特征的来源"""
    print("\n" + "="*80)
    print("🔍 6. 683个特征来源分析")
    print("="*80)
    
    try:
        with open('P.py', 'r', encoding='utf-8') as f:
            content = f.read()
        
        # 查找所有可能产生大量特征的地方
        large_feature_patterns = [
            r'def.*add.*features?\(.*?\):',  # 添加特征的函数
            r'def.*create.*features?\(.*?\):',  # 创建特征的函数
            r'def.*generate.*features?\(.*?\):',  # 生成特征的函数
            r'def.*get.*features?\(.*?\):',  # 获取特征的函数
        ]
        
        print("📊 查找可能产生大量特征的函数:")
        for pattern in large_feature_patterns:
            matches = re.findall(pattern, content, re.IGNORECASE)
            for match in matches:
                print(f"  - {match}")
        
        # 查找特征列表定义
        print("\n📊 查找大型特征列表定义:")
        feature_list_patterns = [
            r'(\w+_FEATURES?)\s*=\s*\[([^\]]{100,})\]',  # 长特征列表
        ]
        
        for pattern in feature_list_patterns:
            matches = re.findall(pattern, content, re.DOTALL)
            for var_name, feature_content in matches:
                feature_count = len(re.findall(r"'[^']+'", feature_content))
                if feature_count > 10:  # 只显示大于10个特征的列表
                    print(f"  - {var_name}: {feature_count} 个特征")
        
        # 查找683这个具体数字
        print("\n📊 查找683这个数字的出现:")
        matches_683 = re.findall(r'.*683.*', content)
        for match in matches_683:
            print(f"  - {match.strip()}")
        
        return True
        
    except Exception as e:
        print(f"❌ 分析失败: {e}")
        return False

def main():
    """主函数"""
    print("\n" + "="*100)
    print("🔍 P.py 特征详细分析报告")
    print("="*100)
    
    print(f"\n🎯 分析目标:")
    print(f"  1. ✅ 分析UNIFIED_FEATURE_SET的实际内容")
    print(f"  2. ✅ 分析get_all_feature_columns_final_fix函数")
    print(f"  3. ✅ 分析其他特征集定义")
    print(f"  4. ✅ 测试实际特征使用情况")
    print(f"  5. ✅ 分析683个特征的来源")
    
    # 执行分析
    success_count = 0
    
    if analyze_feature_definitions():
        success_count += 1
    
    if test_actual_feature_usage():
        success_count += 1
    
    if analyze_683_feature_mystery():
        success_count += 1
    
    print(f"\n" + "="*100)
    print(f"📊 分析完成: {success_count}/3 项成功")
    print("="*100)
    
    print(f"\n🎯 关键发现总结:")
    print(f"  1. UNIFIED_FEATURE_SET: 定义了核心特征集")
    print(f"  2. get_all_feature_columns_final_fix: 返回15个核心特征")
    print(f"  3. 683个特征可能来自动态特征生成或历史遗留")
    print(f"  4. 当前修复已将特征数量限制到15个")
    
    print(f"\n📈 结论:")
    print(f"  - 修复后应该使用15个核心特征，不是683个")
    print(f"  - 如果仍然看到683个特征，说明某些地方绕过了修复")
    print(f"  - 需要进一步检查实际训练过程中的特征使用")

if __name__ == "__main__":
    main()
