#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
🧪 测试P.py修复效果
"""

import logging
import sys
import os

# 设置日志
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def test_fixes():
    """测试修复效果"""
    try:
        # 导入P模块
        import P
        
        # 测试1: 检查特征优化模式
        if hasattr(P, 'ENABLE_FEATURE_OPTIMIZATION'):
            logging.info(f"✅ 特征优化模式: {P.ENABLE_FEATURE_OPTIMIZATION}")
        else:
            logging.error("❌ 缺少ENABLE_FEATURE_OPTIMIZATION")
        
        # 测试2: 检查统一特征集
        if hasattr(P, 'UNIFIED_FEATURE_SET'):
            logging.info(f"✅ 统一特征集: {len(P.UNIFIED_FEATURE_SET)}个特征")
        else:
            logging.error("❌ 缺少UNIFIED_FEATURE_SET")
        
        # 测试3: 检查标准化参数
        if hasattr(P, 'NORMALIZATION_PARAMS'):
            params = P.NORMALIZATION_PARAMS
            if '首板' in params and 'MAIN' in params['首板']:
                logging.info("✅ 首板策略标准化参数存在")
            if '连板' in params and 'MAIN' in params['连板']:
                logging.info("✅ 连板策略标准化参数存在")
        else:
            logging.error("❌ 缺少NORMALIZATION_PARAMS")
        
        # 测试4: 检查关键函数
        functions = ['get_all_feature_columns', 'ensure_feature_consistency_fixed', 'validate_prediction_diversity_fixed']
        for func in functions:
            if hasattr(P, func):
                logging.info(f"✅ 函数存在: {func}")
            else:
                logging.error(f"❌ 函数缺失: {func}")
        
        logging.info("🎉 修复效果测试完成")
        return True
        
    except Exception as e:
        logging.error(f"❌ 测试失败: {e}")
        return False

if __name__ == "__main__":
    test_fixes()
